# Análise de Riscos - Sistema Redis de Emails

## Riscos Identificados e Estratégias de Mitigação

### 1. RISCO ALTO: Perda de Conexão Redis

#### Descrição
Falha na conexão com o servidor Redis pode interromper completamente o envio de emails.

#### Impacto
- **Severidade**: Alta
- **Probabilidade**: Média
- **Consequências**: Interrupção total do envio de emails

#### Estratégias de Mitigação Implementadas

1. **Reconexão Automática**:
   ```javascript
   // Implementado em utils/redisClient.js
   reconnectStrategy: (retries) => {
     const delay = Math.min(Math.pow(2, retries) * 100, 10000);
     return delay; // Exponential backoff até 10s
   }
   ```

2. **Fallback para Sistema Legacy**:
   ```javascript
   // Implementado em index.js
   if (useEmailRedis) {
     try {
       await emailCronRedis();
       await emailSendMessages();
     } catch (redisError) {
       console.error('Redis system failed, falling back to legacy');
       await emailCron(); // Fallback automático
     }
   }
   ```

3. **Health Check Contínuo**:
   ```javascript
   // Verificação de saúde a cada execução
   const client = await getRedisClient();
   if (!client || !client.isOpen) {
     throw new Error('Redis not available');
   }
   ```

#### Procedimentos de Emergência
```bash
# Desativação imediata em caso de falha crítica
firebase functions:config:set email.redis_enabled="false"
firebase deploy --only functions

# Verificar se sistema legacy assumiu
firebase functions:log | grep "Using legacy email system"
```

---

### 2. RISCO MÉDIO: Emails Duplicados

#### Descrição
Durante a migração, emails podem ser enviados tanto pelo sistema legacy quanto pelo Redis.

#### Impacto
- **Severidade**: Média
- **Probabilidade**: Alta (durante migração)
- **Consequências**: Experiência ruim do usuário, possível spam

#### Estratégias de Mitigação Implementadas

1. **Controle Exclusivo por Variável**:
   ```javascript
   // Sistema mutuamente exclusivo
   if (useEmailRedis) {
     // Apenas sistema Redis
     promises.push(emailCronRedis());
     promises.push(emailSendMessages());
   } else {
     // Apenas sistema legacy
     promises.push(emailCron());
   }
   ```

2. **Marcação de Processamento**:
   ```javascript
   // Sistema Redis marca cronjobs como executados
   await email.cronjob_ref.update({
     executed: true,
     execution_date: momentNow().format(CONSTANTS.MOMENT_ISO),
   });
   ```

3. **Logs de Auditoria**:
   ```javascript
   // Rastreamento detalhado de cada email
   console.log(`EMAIL SENT > System: Redis, ID: ${emailId}, To: ${email.to}`);
   ```

#### Procedimentos de Prevenção
```bash
# Verificar configuração antes do deploy
firebase functions:config:get | grep email.redis_enabled

# Monitorar logs para duplicatas
firebase functions:log | grep "EMAIL.*SENT" | sort | uniq -d
```

---

### 3. RISCO MÉDIO: Perda de Dados na Fila Redis

#### Descrição
Falha no servidor Redis pode resultar em perda de emails agendados na fila.

#### Impacto
- **Severidade**: Média
- **Probabilidade**: Baixa
- **Consequências**: Emails agendados não enviados

#### Estratégias de Mitigação Implementadas

1. **Persistência Redis Configurada**:
   ```bash
   # Configuração recomendada para Redis
   save 900 1      # Snapshot a cada 15min se 1+ mudança
   save 300 10     # Snapshot a cada 5min se 10+ mudanças
   save 60 10000   # Snapshot a cada 1min se 10000+ mudanças
   appendonly yes  # AOF habilitado
   ```

2. **Backup Automático no Firestore**:
   ```javascript
   // Emails falhados são salvos no Firestore
   await FirestoreRef.collection("emails_failed").doc(email.id).set({
     ...failedEmail,
     moved_to_failed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
   });
   ```

3. **Reprocessamento de Emails Pendentes**:
   ```javascript
   // Sistema verifica emails não processados no Firestore
   const pendingEmails = await FirestoreRef.collection("emails")
     .where("sent", "==", false)
     .where("sending", "==", false)
     .get();
   ```

#### Procedimentos de Recuperação
```bash
# Verificar emails pendentes no Firestore
# Reprocessar emails não enviados
# Verificar integridade da fila Redis
redis-cli -h host -p port -a password ZCARD email:scheduled_messages
```

---

### 4. RISCO BAIXO: Performance Degradada

#### Descrição
Alto volume de emails pode sobrecarregar o sistema Redis ou os provedores de email.

#### Impacto
- **Severidade**: Baixa
- **Probabilidade**: Média
- **Consequências**: Lentidão no envio, timeouts

#### Estratégias de Mitigação Implementadas

1. **Rate Limiting**:
   ```javascript
   // Delay entre emails para evitar sobrecarga
   if (emailIndex < totalEmails) {
     await new Promise(resolve => setTimeout(resolve, 100));
   }
   ```

2. **Batch Size Configurável**:
   ```javascript
   // Processamento em lotes pequenos
   const { batchSize = 20 } = options; // Menor que mensagens
   ```

3. **Timeout Configurável**:
   ```javascript
   // Timeout ajustável por ambiente
   connectTimeout: parseInt(process.env.REDIS_TIMEOUT || "10000")
   ```

4. **Monitoramento de Performance**:
   ```javascript
   // Logs de tempo de processamento
   console.log(`Processed ${processedEmails.length} emails in ${duration}ms`);
   ```

---

### 5. RISCO BAIXO: Incompatibilidade de Versões

#### Descrição
Atualizações do Redis client ou Firebase Functions podem quebrar compatibilidade.

#### Impacto
- **Severidade**: Baixa
- **Probabilidade**: Baixa
- **Consequências**: Sistema não funciona após deploy

#### Estratégias de Mitigação Implementadas

1. **Versionamento Fixo**:
   ```json
   // package.json com versões específicas
   "redis": "^5.0.1",
   "firebase-functions": "^3.16.0"
   ```

2. **Testes Automatizados**:
   ```javascript
   // Script de teste de conexão
   const testRedisConnection = async () => {
     // Testa todas as operações críticas
   };
   ```

3. **Rollback Rápido**:
   ```bash
   # Procedimento de rollback em caso de falha
   firebase functions:config:set email.redis_enabled="false"
   firebase deploy --only functions
   ```

---

## Matriz de Riscos

| Risco | Probabilidade | Impacto | Severidade | Mitigação |
|-------|---------------|---------|------------|-----------|
| Perda conexão Redis | Média | Alto | **ALTO** | ✅ Implementada |
| Emails duplicados | Alta | Médio | **MÉDIO** | ✅ Implementada |
| Perda dados fila | Baixa | Médio | **MÉDIO** | ✅ Implementada |
| Performance degradada | Média | Baixo | **BAIXO** | ✅ Implementada |
| Incompatibilidade versões | Baixa | Baixo | **BAIXO** | ✅ Implementada |

## Plano de Contingência

### Cenário 1: Falha Total do Redis
```bash
# 1. Desativar sistema Redis imediatamente
firebase functions:config:set email.redis_enabled="false"

# 2. Deploy emergencial
firebase deploy --only functions

# 3. Verificar funcionamento do sistema legacy
firebase functions:log | grep "emailCron"

# 4. Investigar causa da falha Redis
# 5. Reprocessar emails perdidos se necessário
```

### Cenário 2: Performance Crítica
```bash
# 1. Reduzir batch size
firebase functions:config:set email.batch_size="5"

# 2. Aumentar timeout
firebase functions:config:set email.timeout="30000"

# 3. Deploy
firebase deploy --only functions

# 4. Monitorar melhoria
firebase functions:log --follow | grep "Processadas.*emails"
```

### Cenário 3: Emails Duplicados Detectados
```bash
# 1. Verificar configuração
firebase functions:config:get | grep email.redis_enabled

# 2. Se necessário, desativar temporariamente
firebase functions:config:set email.redis_enabled="false"

# 3. Investigar logs para identificar causa
firebase functions:log | grep "EMAIL.*SENT" | sort

# 4. Corrigir configuração e reativar
```

## Monitoramento Contínuo

### Alertas Críticos
```javascript
// Implementar alertas para:
// 1. Falhas de conexão Redis > 5 min
// 2. Taxa de falha de emails > 10%
// 3. Fila Redis > 1000 emails
// 4. Tempo de processamento > 5 min
```

### Métricas de Saúde
```bash
# Verificações diárias recomendadas:
# 1. Status da conexão Redis
# 2. Tamanho das filas (scheduled + failed)
# 3. Taxa de sucesso/falha dos emails
# 4. Tempo médio de processamento
# 5. Uso de memória Redis
```

### Dashboard de Monitoramento
```javascript
// Métricas recomendadas para dashboard:
const metrics = {
  emailsProcessed: 0,
  emailsSent: 0,
  emailsFailed: 0,
  averageProcessingTime: 0,
  redisConnectionStatus: 'healthy',
  queueSize: 0,
  failedQueueSize: 0
};
```

## Procedimentos de Rollback

### Rollback Imediato (< 5 minutos)
```bash
# Para emergências críticas
firebase functions:config:set email.redis_enabled="false"
firebase deploy --only functions --force
```

### Rollback Planejado (< 30 minutos)
```bash
# 1. Notificar equipe
# 2. Verificar emails na fila Redis
# 3. Processar emails pendentes se possível
# 4. Desativar sistema Redis
# 5. Deploy
# 6. Verificar funcionamento legacy
# 7. Documentar motivo do rollback
```

### Rollback Completo (< 2 horas)
```bash
# 1. Backup completo da configuração atual
# 2. Revert do código para versão anterior
# 3. Restaurar configurações Firebase
# 4. Deploy completo
# 5. Testes de funcionalidade
# 6. Análise post-mortem
```

## Conclusão

O sistema foi projetado com múltiplas camadas de proteção e procedimentos de contingência bem definidos. Os riscos identificados possuem estratégias de mitigação implementadas e testadas. O rollback para o sistema legacy é simples e rápido, garantindo que o envio de emails nunca seja completamente interrompido.

A migração gradual e o monitoramento contínuo minimizam os riscos operacionais, permitindo uma transição segura para a nova arquitetura Redis.
