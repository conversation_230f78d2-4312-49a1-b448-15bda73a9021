# Guia de Início <PERSON>o - Sistema Redis de Emails

## Teste Rápido (5 minutos)

### 1. Verificar Pré-requisitos
```bash
# Verificar se Redis está rodando
redis-cli ping
# Deve retornar: PONG

# Verificar se as dependências estão instaladas
cd functions
npm list redis
# Deve mostrar: redis@5.0.1
```

### 2. Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp functions/.env.example functions/.env

# Editar configurações (manter EMAIL_REDIS_ENABLED=false por enquanto)
nano functions/.env
```

### 3. Executar Testes Automatizados
```bash
# Executar suite de testes
cd functions
node test-email-redis-system.js

# Resultado esperado: "🎉 Todos os testes passaram!"
```

### 4. Verificar Integração
```bash
# Verificar se os módulos são importados corretamente
node -e "
const { emailOrganizeMessages } = require('./emailOrganize');
const { emailSendMessages } = require('./emailSendMessages');
console.log('✅ Módulos carregados com sucesso');
"
```

## Ativação em Desenvolvimento

### 1. Ativar Sistema Redis
```bash
# Editar .env
echo "EMAIL_REDIS_ENABLED=true" >> functions/.env
```

### 2. Deploy Local (Emulador)
```bash
# Iniciar emulador Firebase
firebase emulators:start --only functions

# Em outro terminal, verificar logs
firebase functions:log --follow
```

### 3. Monitorar Funcionamento
```bash
# Verificar se sistema Redis está ativo
firebase functions:log | grep "Using Redis-based email system"

# Monitorar processamento de emails
firebase functions:log | grep "EMAILCRON"
```

## Ativação em Produção

### 1. Configurar Firebase Functions
```bash
# Definir variável de ambiente
firebase functions:config:set email.redis_enabled="true"

# Verificar configuração
firebase functions:config:get
```

### 2. Deploy
```bash
# Deploy apenas das functions
firebase deploy --only functions

# Monitorar logs após deploy
firebase functions:log --follow
```

### 3. Rollback (se necessário)
```bash
# Desativar sistema Redis
firebase functions:config:set email.redis_enabled="false"

# Deploy imediato
firebase deploy --only functions --force
```

## Comandos Úteis

### Monitoramento
```bash
# Status do sistema
firebase functions:log | grep "CRON1MINUTES > Using"

# Emails processados
firebase functions:log | grep "Processadas.*emails"

# Erros
firebase functions:log | grep "ERROR"
```

### Redis
```bash
# Verificar fila de emails
redis-cli ZCARD email:scheduled_messages

# Verificar emails falhados
redis-cli ZCARD email:failed_messages

# Limpar filas (CUIDADO!)
redis-cli DEL email:scheduled_messages email:failed_messages
```

### Debugging
```bash
# Logs detalhados do sistema Redis
firebase functions:log | grep "EMAILCRON > EMAILORGANIZE"

# Logs de envio
firebase functions:log | grep "EMAIL SENT"

# Logs de retry
firebase functions:log | grep "EMAIL WILL RETRY"
```

## Checklist de Verificação

### ✅ Pré-Deploy
- [ ] Redis configurado e funcionando
- [ ] Testes automatizados passando
- [ ] Variáveis de ambiente configuradas
- [ ] Sistema legacy funcionando normalmente

### ✅ Pós-Deploy
- [ ] Logs mostram "Using Redis-based email system"
- [ ] Emails sendo processados sem erros
- [ ] Nenhum email duplicado detectado
- [ ] Performance dentro do esperado

### ✅ Monitoramento Contínuo
- [ ] Verificar logs a cada 30 minutos nas primeiras 4 horas
- [ ] Monitorar métricas de performance
- [ ] Verificar se não há emails órfãos no Redis
- [ ] Confirmar que emails estão sendo entregues

## Solução de Problemas Rápidos

### Problema: "Redis client not available"
```bash
# Verificar conexão
redis-cli -h seu-host -p 6379 -a sua-senha ping

# Verificar configurações
firebase functions:config:get | grep redis
```

### Problema: "Using legacy email system" (quando deveria usar Redis)
```bash
# Verificar variável de ambiente
firebase functions:config:get | grep email.redis_enabled

# Reconfigurar se necessário
firebase functions:config:set email.redis_enabled="true"
firebase deploy --only functions
```

### Problema: Emails duplicados
```bash
# Verificar se ambos os sistemas estão ativos
firebase functions:log | grep "emailCron\|EMAILCRON"

# Desativar um dos sistemas
firebase functions:config:set email.redis_enabled="false"
firebase deploy --only functions
```

### Problema: Performance lenta
```bash
# Reduzir batch size
firebase functions:config:set email.batch_size="10"

# Aumentar timeout
firebase functions:config:set email.timeout="30000"

# Deploy
firebase deploy --only functions
```

## Contatos de Suporte

### Documentação
- **README completo**: `docs/email-system-redis/README.md`
- **Guia de configuração**: `docs/email-system-redis/configuration-guide.md`
- **Análise de riscos**: `docs/email-system-redis/risk-analysis.md`

### Comandos de Emergência
```bash
# Rollback imediato
firebase functions:config:set email.redis_enabled="false"
firebase deploy --only functions --force

# Verificar sistema legacy
firebase functions:log | grep "Using legacy email system"
```

---

**⚠️ Importante**: Sempre teste em ambiente de desenvolvimento antes de ativar em produção. Mantenha o sistema legacy funcionando durante a migração para garantir continuidade do serviço.
