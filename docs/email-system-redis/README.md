# Sistema de Envio de Emails baseado em Redis

## Visão Geral

Este documento descreve a implementação do novo sistema de envio de emails baseado na arquitetura Redis, seguindo o mesmo padrão bem-sucedido do sistema shotxCron. O sistema foi projetado para ser uma migração gradual e segura do sistema legacy baseado puramente em Firestore.

## Arquitetura do Sistema

### Componentes Principais

1. **emailCronRedis** (`functions/mailing/index.js`)
   - Responsável por buscar emails agendados no Firestore
   - Coleta emails de duas fontes: cronjobs e emails diretos
   - Não marca emails como processados (deixa para o sistema Redis gerenciar)

2. **emailOrganizeMessages** (`functions/emailOrganize/index.js`)
   - Prepara emails individuais para envio
   - Aplica shortcodes e templates
   - Salva emails no Redis com timestamps de agendamento
   - Marca cronjobs como executados após salvar no Redis

3. **emailSendMessages** (`functions/emailSendMessages/index.js`)
   - Processa emails do Redis e os envia
   - Implementa sistema de retry com exponential backoff
   - Gerencia filas de falha para emails problemáticos
   - Remove emails do Redis após envio bem-sucedido

### Fluxo de Dados

```
Firestore (Emails Agendados)
         ↓
   emailCronRedis
         ↓
 emailOrganizeMessages
         ↓
   Redis (Fila Ordenada)
         ↓
  emailSendMessages
         ↓
   Provedores de Email
   (Mailgun/Resend/SMTP)
```

## Funcionalidades Implementadas

### 1. Sistema de Retry Inteligente
- **Exponential Backoff**: 2s, 4s, 8s entre tentativas
- **Máximo de 3 tentativas** por email por padrão
- **Reagendamento automático** para tentativas futuras
- **Dead Letter Queue** para emails que falharam definitivamente

### 2. Processamento em Lote
- **Batch size configurável** (padrão: 20 emails por execução)
- **Rate limiting** com delay de 100ms entre emails
- **Processamento sequencial** para evitar sobrecarga

### 3. Múltiplos Provedores
- **Mantém compatibilidade** com Mailgun, Resend e SMTP
- **Reutiliza funções existentes** de envio de email
- **Fallback automático** entre provedores (funcionalidade existente)

### 4. Observabilidade Completa
- **Logs detalhados** em cada etapa do processo
- **Rastreamento individual** de cada email
- **Métricas de performance** (tentativas, tempo de processamento)
- **Auditoria no Firestore** para emails falhados

### 5. Migração Gradual
- **Sistema dual** (legacy + Redis) funcionando em paralelo
- **Ativação via variável de ambiente** (`EMAIL_REDIS_ENABLED`)
- **Zero downtime** durante a migração
- **Rollback simples** alterando variável de ambiente

## Estrutura de Dados Redis

### Chaves Utilizadas

```
email:scheduled_messages     # Lista ordenada de emails agendados
email:message:{id}          # Dados individuais de cada email
email:failed_messages       # Lista de emails que falharam
email:failed:{id}           # Dados de emails falhados
```

### Formato dos Dados

```javascript
{
  id: "email_1234567890_abc123",
  to: "<EMAIL>",
  from: "<EMAIL>",
  subject: "Assunto do Email",
  html: "<html>Conteúdo processado</html>",
  scheduled_date: "2024-01-15T10:00:00.000Z",
  _scheduled_timestamp: 1705312800000,
  _scheduled_iso: "2024-01-15T10:00:00.000Z",
  _created_at: "2024-01-15T09:55:00.000Z",
  _type: "email",
  redis_key: "email:message:email_1234567890_abc123",
  attempts: 0,
  firestore_ref: FirestoreDocumentReference
}
```

## Configuração e Ativação

### Variáveis de Ambiente

Adicione ao arquivo `functions/.env`:

```bash
# Ativar sistema Redis para emails
EMAIL_REDIS_ENABLED=true

# Configurações Redis (já existentes)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=sua_senha_redis
REDIS_DATABASE=0
REDIS_TIMEOUT=10000
```

### Configuração Firebase Functions

As configurações Redis devem estar definidas no Firebase Functions:

```bash
firebase functions:config:set redis.host="seu-host-redis"
firebase functions:config:set redis.port="6379"
firebase functions:config:set redis.password="sua-senha"
firebase functions:config:set redis.database="0"
```

### Ativação do Sistema

1. **Desenvolvimento/Teste**:
   ```bash
   # No arquivo functions/.env
   EMAIL_REDIS_ENABLED=true
   ```

2. **Produção**:
   ```bash
   # Definir variável de ambiente no Firebase Functions
   firebase functions:config:set email.redis_enabled="true"
   
   # Ou usar variável de ambiente do sistema
   export EMAIL_REDIS_ENABLED=true
   ```

3. **Deploy**:
   ```bash
   firebase deploy --only functions
   ```

## Monitoramento e Debugging

### Logs Importantes

```bash
# Verificar se o sistema Redis está ativo
grep "Using Redis-based email system" logs/

# Monitorar processamento de emails
grep "EMAILCRON > EMAILORGANIZE" logs/

# Verificar envios bem-sucedidos
grep "EMAIL SENT SUCCESSFULLY" logs/

# Identificar falhas
grep "EMAIL FAILED" logs/

# Monitorar retry attempts
grep "EMAIL WILL RETRY" logs/
```

### Métricas de Performance

O sistema registra automaticamente:
- Número de emails processados por execução
- Tempo de processamento por email
- Taxa de sucesso/falha
- Número de tentativas por email
- Emails movidos para dead letter queue

### Comandos de Diagnóstico

```javascript
// Verificar emails na fila (via console do Firebase)
const { getScheduledEmailsFromRedis } = require('./emailSendMessages');
await getScheduledEmailsFromRedis();

// Limpar fila de emails (CUIDADO!)
const { clearAllEmails } = require('./emailSendMessages');
await clearAllEmails();

// Remover email específico
const { deleteEmail } = require('./emailSendMessages');
await deleteEmail('email_id_aqui');
```

## Comparação: Sistema Legacy vs Redis

| Aspecto | Sistema Legacy | Sistema Redis |
|---------|---------------|---------------|
| **Armazenamento** | Firestore apenas | Firestore + Redis |
| **Processamento** | Síncrono | Assíncrono com fila |
| **Retry** | Limitado | Exponential backoff |
| **Escalabilidade** | Limitada | Alta |
| **Observabilidade** | Básica | Avançada |
| **Rate Limiting** | Não | Sim |
| **Dead Letter Queue** | Não | Sim |
| **Rollback** | Difícil | Simples |

## Benefícios da Migração

### 1. **Confiabilidade**
- Sistema de retry robusto
- Dead letter queue para emails problemáticos
- Transações atômicas Redis

### 2. **Performance**
- Processamento assíncrono
- Rate limiting inteligente
- Batch processing otimizado

### 3. **Escalabilidade**
- Fila Redis suporta milhões de emails
- Processamento distribuído
- Auto-scaling baseado na fila

### 4. **Manutenibilidade**
- Logs estruturados
- Métricas detalhadas
- Debugging simplificado

### 5. **Flexibilidade**
- Configuração via variáveis de ambiente
- Migração gradual
- Rollback instantâneo

## Próximos Passos

1. **Teste em Desenvolvimento**
   - Ativar `EMAIL_REDIS_ENABLED=true`
   - Monitorar logs por 24-48 horas
   - Verificar métricas de performance

2. **Teste em Staging**
   - Deploy em ambiente de staging
   - Teste com volume real de emails
   - Validar todos os provedores de email

3. **Migração Gradual em Produção**
   - Ativar para 10% do tráfego
   - Monitorar por 1 semana
   - Aumentar gradualmente até 100%

4. **Remoção do Sistema Legacy**
   - Após 1 mês de operação estável
   - Manter código legacy por mais 1 mês
   - Documentar lições aprendidas
