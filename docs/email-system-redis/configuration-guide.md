# Guia de Configuração - Sistema Redis de Emails

## Pré-requisitos

### 1. Redis Server
- **Versão mínima**: Redis 5.0+
- **Memória recomendada**: <PERSON><PERSON><PERSON> 512MB para desenvolvimento, 2GB+ para produção
- **Persistência**: Configurar RDB + AOF para durabilidade
- **Autenticação**: Senha obrigatória em produção

### 2. Node.js e Dependências
- **Node.js**: Versão 18+ (compatível com Firebase Functions)
- **Redis client**: v5.0.1 (já instalado no projeto)
- **Firebase Functions**: v3.16.0+

## Configuração Passo a Passo

### Etapa 1: Configuração do Redis

#### Desenvolvimento Local
```bash
# Instalar Redis (Ubuntu/Debian)
sudo apt update
sudo apt install redis-server

# Configurar senha
sudo nano /etc/redis/redis.conf
# Adicionar linha: requirepass sua_senha_aqui

# Reiniciar Redis
sudo systemctl restart redis-server

# Testar conexão
redis-cli -a sua_senha_aqui ping
# Deve retornar: PONG
```

#### Produção (Redis Cloud/AWS ElastiCache)
```bash
# Exemplo de configuração para Redis Cloud
REDIS_HOST=redis-12345.c1.us-east-1-1.ec2.cloud.redislabs.com
REDIS_PORT=12345
REDIS_PASSWORD=sua_senha_complexa
```

### Etapa 2: Configuração das Variáveis de Ambiente

#### Arquivo `.env` (Desenvolvimento)
```bash
# Copiar arquivo de exemplo
cp functions/.env.example functions/.env

# Editar configurações
nano functions/.env
```

Conteúdo do arquivo `.env`:
```bash
# Chat API Configuration
CHAT_API_URL=https://shotxv2dev.qi.plus

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=sua_senha_redis
REDIS_DATABASE=0
REDIS_TIMEOUT=10000

# Email System Configuration
EMAIL_REDIS_ENABLED=false  # Manter false inicialmente

# Environment
NODE_ENV=development
```

#### Firebase Functions Config (Produção)
```bash
# Configurar Redis
firebase functions:config:set redis.host="seu-host-redis"
firebase functions:config:set redis.port="6379"
firebase functions:config:set redis.password="sua-senha"
firebase functions:config:set redis.database="0"

# Verificar configuração
firebase functions:config:get
```

### Etapa 3: Teste da Conexão Redis

#### Script de Teste
Criar arquivo `functions/test-redis.js`:
```javascript
const { getRedisClient } = require('./utils/redisClient');

async function testRedisConnection() {
  try {
    console.log('Testando conexão Redis...');
    
    const client = await getRedisClient();
    if (!client) {
      throw new Error('Falha ao conectar no Redis');
    }
    
    // Teste básico
    await client.set('test:connection', 'OK');
    const result = await client.get('test:connection');
    
    if (result === 'OK') {
      console.log('✅ Conexão Redis funcionando!');
      await client.del('test:connection');
    } else {
      throw new Error('Teste de escrita/leitura falhou');
    }
    
  } catch (error) {
    console.error('❌ Erro na conexão Redis:', error.message);
    process.exit(1);
  }
}

testRedisConnection();
```

Executar teste:
```bash
cd functions
node test-redis.js
```

### Etapa 4: Configuração Gradual

#### Fase 1: Preparação (EMAIL_REDIS_ENABLED=false)
```bash
# 1. Deploy do código sem ativar
firebase deploy --only functions

# 2. Verificar logs para erros
firebase functions:log

# 3. Confirmar que sistema legacy ainda funciona
```

#### Fase 2: Teste Inicial (EMAIL_REDIS_ENABLED=true)
```bash
# 1. Ativar sistema Redis
firebase functions:config:set email.redis_enabled="true"

# 2. Deploy
firebase deploy --only functions

# 3. Monitorar logs intensivamente
firebase functions:log --follow
```

#### Fase 3: Monitoramento
```bash
# Verificar processamento de emails
firebase functions:log | grep "EMAILCRON"

# Verificar erros
firebase functions:log | grep "ERROR"

# Verificar métricas
firebase functions:log | grep "Processadas.*emails"
```

### Etapa 5: Configurações Avançadas

#### Otimização de Performance
```bash
# Aumentar timeout para emails grandes
firebase functions:config:set email.timeout="30000"

# Configurar batch size
firebase functions:config:set email.batch_size="15"

# Configurar max retries
firebase functions:config:set email.max_retries="5"
```

#### Configuração de Alertas
```javascript
// Adicionar ao functions/index.js
const alertOnEmailFailures = async (failureCount) => {
  if (failureCount > 10) {
    await adminMail({
      to: '<EMAIL>',
      subject: 'ALERTA: Falhas no sistema de email',
      html: `Detectadas ${failureCount} falhas no envio de emails.`
    });
  }
};
```

## Configurações por Ambiente

### Desenvolvimento
```bash
# functions/.env
EMAIL_REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=dev_password
NODE_ENV=development
```

### Staging
```bash
# Firebase Functions Config
firebase use staging
firebase functions:config:set email.redis_enabled="true"
firebase functions:config:set redis.host="staging-redis.example.com"
firebase functions:config:set redis.password="staging_password"
```

### Produção
```bash
# Firebase Functions Config
firebase use production
firebase functions:config:set email.redis_enabled="true"
firebase functions:config:set redis.host="prod-redis.example.com"
firebase functions:config:set redis.password="prod_password_complexa"
```

## Procedimentos de Ativação/Desativação

### Ativar Sistema Redis

1. **Verificar pré-requisitos**:
   ```bash
   # Testar conexão Redis
   node functions/test-redis.js
   
   # Verificar configurações
   firebase functions:config:get
   ```

2. **Ativar gradualmente**:
   ```bash
   # Ativar
   firebase functions:config:set email.redis_enabled="true"
   
   # Deploy
   firebase deploy --only functions
   
   # Monitorar por 30 minutos
   firebase functions:log --follow | grep "EMAILCRON"
   ```

3. **Verificar funcionamento**:
   ```bash
   # Verificar emails sendo processados
   firebase functions:log | grep "EMAIL SENT SUCCESSFULLY"
   
   # Verificar se não há emails duplicados
   firebase functions:log | grep "duplicate"
   ```

### Desativar Sistema Redis (Rollback)

1. **Desativação imediata**:
   ```bash
   # Desativar
   firebase functions:config:set email.redis_enabled="false"
   
   # Deploy urgente
   firebase deploy --only functions
   ```

2. **Verificar rollback**:
   ```bash
   # Confirmar uso do sistema legacy
   firebase functions:log | grep "Using legacy email system"
   
   # Verificar se emails continuam sendo enviados
   firebase functions:log | grep "emailCron"
   ```

3. **Limpeza (opcional)**:
   ```bash
   # Limpar fila Redis se necessário
   redis-cli -h seu-host -p 6379 -a sua-senha
   > DEL email:scheduled_messages
   > DEL email:failed_messages
   ```

## Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão Redis
```
Error: ECONNREFUSED 127.0.0.1:6379
```
**Solução**:
- Verificar se Redis está rodando: `sudo systemctl status redis-server`
- Verificar configurações de host/porta
- Verificar firewall/security groups

#### 2. Erro de Autenticação
```
Error: NOAUTH Authentication required
```
**Solução**:
- Verificar senha no arquivo `.env` ou Firebase config
- Testar conexão manual: `redis-cli -h host -p port -a password`

#### 3. Emails Duplicados
```
Email sent twice: same ID found
```
**Solução**:
- Verificar se ambos os sistemas estão ativos
- Confirmar valor de `EMAIL_REDIS_ENABLED`
- Verificar logs de ambos os sistemas

#### 4. Performance Degradada
```
Timeout processing emails
```
**Solução**:
- Reduzir batch size: `email.batch_size="10"`
- Aumentar timeout: `email.timeout="60000"`
- Verificar latência Redis

### Comandos de Diagnóstico

```bash
# Verificar status do sistema
firebase functions:log | grep "CRON1MINUTES > Using"

# Contar emails na fila Redis
redis-cli -h host -p port -a password ZCARD email:scheduled_messages

# Verificar emails falhados
redis-cli -h host -p port -a password ZCARD email:failed_messages

# Monitorar processamento em tempo real
firebase functions:log --follow | grep "EMAILCRON"
```

## Checklist de Configuração

- [ ] Redis server configurado e rodando
- [ ] Senha Redis definida e testada
- [ ] Arquivo `.env` criado com configurações corretas
- [ ] Firebase Functions config definido para produção
- [ ] Teste de conexão Redis executado com sucesso
- [ ] Deploy realizado sem erros
- [ ] Sistema legacy funcionando antes da migração
- [ ] Logs monitorados por pelo menos 1 hora após ativação
- [ ] Procedimento de rollback testado em ambiente de desenvolvimento
- [ ] Alertas configurados para falhas críticas
- [ ] Documentação atualizada com configurações específicas do ambiente
