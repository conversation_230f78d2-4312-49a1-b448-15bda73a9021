const {
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS, momentNow } = require("../init");
const { sendMail } = require("../mailing");

/**
 * Função principal para processar e enviar emails do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} - Lista de emails processados
 */
const emailSendMessages = async (options = {}) => {
  console.log(
    "EMAILCRON > SEND_MESSAGES > START > Iniciando processamento de emails do Redis"
  );

  try {
    // Log das opções recebidas
    const { batchSize = 20, dryRun = false, maxRetries = 3 } = options;
    console.log(
      `EMAILCRON > SEND_MESSAGES > OPTIONS > BatchSize: ${batchSize}, DryRun: ${dryRun}, MaxRetries: ${maxRetries}`
    );

    // Processar os emails agendados
    console.log(
      "EMAILCRON > SEND_MESSAGES > PROCESS > Iniciando processamento de emails agendados"
    );
    const processedEmails = await processScheduledEmails(options);

    console.log(
      `EMAILCRON > SEND_MESSAGES > COMPLETED > SUCCESS > Processados ${processedEmails.length} emails`
    );

    // Estatísticas dos resultados
    const sentEmails = processedEmails.filter(
      (e) => e.status === "sent"
    ).length;
    const failedEmails = processedEmails.filter(
      (e) => e.status === "failed"
    ).length;
    const simulatedEmails = processedEmails.filter(
      (e) => e.status === "simulated"
    ).length;

    console.log(
      `EMAILCRON > SEND_MESSAGES > STATS > Enviados: ${sentEmails}, Falharam: ${failedEmails}, Simulados: ${simulatedEmails}`
    );

    return processedEmails;
  } catch (error) {
    console.error(`EMAILCRON > SEND_MESSAGES > ERROR > ${error.message}`);
    console.error(`EMAILCRON > SEND_MESSAGES > ERROR > Stack: ${error.stack}`);

    // Se for erro de Redis, tentar fallback para sistema legacy
    if (
      error.message.includes("Redis") ||
      error.message.includes("ECONNREFUSED")
    ) {
      console.log(
        "EMAILCRON > SEND_MESSAGES > FALLBACK > Redis error detected, falling back to legacy system"
      );
      try {
        const { emailCron } = require("../mailing");
        const result = await emailCron();
        console.log(
          "EMAILCRON > SEND_MESSAGES > FALLBACK > SUCCESS > Sistema legacy executado"
        );
        return result;
      } catch (fallbackError) {
        console.error(
          `EMAILCRON > SEND_MESSAGES > FALLBACK > ERROR > ${fallbackError.message}`
        );
      }
    }

    return [];
  }
};

/**
 * Busca e imprime os emails agendados do Redis
 * @returns {Promise<Array>} Lista de emails
 */
const getScheduledEmailsFromRedis = async () => {
  try {
    const scheduledListKey = "email:scheduled_messages";
    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "VIEW",
      {
        limit: 100,
        remove: false,
      }
    );

    console.log(
      `EMAILCRON > GET SCHEDULED EMAILS > Found ${emails.length} emails`
    );

    emails.forEach((email, index) => {
      const scheduledDate = new Date(email._scheduled_timestamp).toISOString();
      console.log(
        `EMAILCRON > EMAIL ${index + 1}: To: ${email.to}, Scheduled: ${scheduledDate}, Subject: ${email.subject || "No subject"}`
      );
    });

    return emails;
  } catch (error) {
    console.error("EMAILCRON > GET SCHEDULED EMAILS > ERROR:", error.message);
    return [];
  }
};

/**
 * Processa emails agendados do Redis e os envia
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de emails a processar por vez (default: 20)
 * @param {boolean} options.dryRun - Se true, não envia os emails, apenas simula (default: false)
 * @param {number} options.maxRetries - Número máximo de tentativas por email (default: 3)
 * @returns {Promise<Array>} Lista de emails processados
 */
const processScheduledEmails = async (options = {}) => {
  console.log(
    "EMAILCRON > PROCESS > START > Iniciando processamento de emails agendados"
  );

  // Definir opções padrão
  const {
    batchSize = 20, // Menor que mensagens devido ao overhead de email
    dryRun = false,
    maxRetries = 3,
  } = options;

  console.log(
    `EMAILCRON > PROCESS > CONFIG > BatchSize: ${batchSize}, DryRun: ${dryRun}, MaxRetries: ${maxRetries}`
  );

  try {
    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "email:scheduled_messages";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    console.log(
      `EMAILCRON > PROCESS > QUERY_TIME > Processando emails até: ${momentNowISO} (${momentNowInstanceTimestamp})`
    );

    // Obter os emails agendados no Redis
    console.log(
      "EMAILCRON > PROCESS > REDIS_QUERY > Buscando emails agendados no Redis"
    );
    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    console.log(
      `EMAILCRON > PROCESS > REDIS_QUERY > SUCCESS > Encontrados ${emails.length} emails`
    );

    if (emails.length === 0) {
      console.log(
        "EMAILCRON > PROCESS > NO_EMAILS > Nenhum email encontrado para processar"
      );
      return [];
    }

    const totalEmails = emails.length;
    console.log(
      `EMAILCRON > PROCESS > BATCH_INFO > Processando ${totalEmails} emails`
    );

    const processedEmails = [];
    let emailIndex = 0;

    // Processar emails sequencialmente para evitar sobrecarga
    for (const email of emails) {
      try {
        const emailId = email.id || `unknown_${emailIndex}`;

        const logPrefix = `EMAILCRON > PROCESS > [${emailIndex + 1}/${totalEmails}] > ${emailId}`;
        console.log(
          `${logPrefix} > START > To: ${email.to}, Scheduled: ${email._scheduled_iso || "unknown"}`
        );

        // Se for modo de simulação, não enviar realmente
        if (dryRun) {
          console.log(
            `${logPrefix} > DRY_RUN > Simulando envio para ${email.to}`
          );

          // Simular atualização no Firestore se houver referência
          if (email.firestore_ref) {
            console.log(
              `${logPrefix} > DRY_RUN > Simularia atualização Firestore`
            );
          }

          // Usar a redis_key do email para remoção correta
          if (email.redis_key) {
            console.log(
              `${logPrefix} > DRY_RUN > Removendo do Redis: ${email.redis_key}`
            );
            await removeMessage(email.redis_key, scheduledListKey);
          }

          console.log(`${logPrefix} > DRY_RUN > SUCCESS > Email simulado`);
          processedEmails.push({ ...email, status: "simulated" });
          emailIndex += 1;
          continue;
        }

        // Tentar enviar o email com retry
        console.log(`${logPrefix} > SEND > Iniciando envio com retry`);
        const sendResult = await sendEmailWithRetry(email, maxRetries);

        if (sendResult.success) {
          console.log(
            `${logPrefix} > SEND > SUCCESS > Email enviado com sucesso`
          );

          // Remover do Redis após envio bem-sucedido
          if (email.redis_key) {
            console.log(
              `${logPrefix} > REDIS_REMOVE > Removendo email do Redis: ${email.redis_key}`
            );
            await removeMessage(email.redis_key, scheduledListKey);
            console.log(
              `${logPrefix} > REDIS_REMOVE > SUCCESS > Email removido do Redis`
            );
          }

          processedEmails.push({ ...email, status: "sent", ...sendResult });
        } else {
          console.error(`${logPrefix} > SEND > ERROR > ${sendResult.error}`);

          // Incrementar contador de tentativas
          const attempts = (email.attempts || 0) + 1;
          console.log(
            `${logPrefix} > RETRY_CHECK > Tentativa ${attempts}/${maxRetries}`
          );

          if (attempts >= maxRetries) {
            console.error(
              `${logPrefix} > MAX_RETRIES > Máximo de tentativas atingido, salvando log e removendo da fila`
            );

            // Salvar log de falha definitiva no Firestore
            await saveFailureLog(email, sendResult.error, attempts);

            // Remover da fila principal
            if (email.redis_key) {
              console.log(
                `${logPrefix} > FAILED_REMOVE > Removendo da fila principal`
              );
              await removeMessage(email.redis_key, scheduledListKey);
            }
          } else {
            console.log(
              `${logPrefix} > RETRY_SCHEDULE > Reagendando para nova tentativa`
            );

            // Atualizar contador de tentativas no Redis
            const updatedEmail = {
              ...email,
              attempts,
              last_error: sendResult.error,
            };
            // Reagendar para tentativa posterior (5 minutos)
            const retryTime = Date.now() + 5 * 60 * 1000;

            console.log(
              `${logPrefix} > RETRY_SCHEDULE > Nova tentativa em: ${new Date(retryTime).toISOString()}`
            );

            // Remover da posição atual
            if (email.redis_key) {
              await removeMessage(email.redis_key, scheduledListKey);
            }

            // Salvar novamente com novo timestamp
            const { saveScheduledMessage } = require("../utils/redisClient");
            await saveScheduledMessage(
              email.redis_key,
              updatedEmail,
              scheduledListKey,
              retryTime
            );

            console.log(
              `${logPrefix} > RETRY_SCHEDULE > SUCCESS > Email reagendado`
            );
          }

          processedEmails.push({
            ...email,
            status: "failed",
            error: sendResult.error,
          });
        }

        emailIndex += 1;

        // Pequeno delay entre emails para evitar rate limiting
        if (emailIndex < totalEmails) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error(
          `EMAILCRON > PROCESS EMAILS > UNEXPECTED ERROR processing email ${emailIndex}:`,
          error.message
        );
        emailIndex += 1;
      }
    }

    console.log(
      `EMAILCRON > PROCESS EMAILS > COMPLETED > Processed: ${processedEmails.length}, Total: ${totalEmails}`
    );

    return processedEmails;
  } catch (error) {
    console.error(
      "EMAILCRON > PROCESS EMAILS > CRITICAL ERROR:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Envia um email com sistema de retry
 * @param {Object} email - Dados do email
 * @param {number} maxRetries - Número máximo de tentativas
 * @returns {Promise<Object>} - Resultado do envio
 */
const sendEmailWithRetry = async (email, maxRetries = 3) => {
  const emailId = email.id || "unknown";
  const logPrefix = `EMAILCRON > SEND_RETRY > ${emailId}`;

  console.log(
    `${logPrefix} > START > Iniciando envio com retry (max: ${maxRetries})`
  );
  let lastError = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(
      `${logPrefix} > ATTEMPT_${attempt} > Tentativa ${attempt}/${maxRetries}`
    );

    try {
      // Criar referência do Firestore se necessário
      let emailRef = email.firestore_ref;
      if (!emailRef && email.id) {
        console.log(
          `${logPrefix} > FIRESTORE_CREATE > Criando documento de rastreamento`
        );
        // Criar documento no Firestore para rastreamento se não existir
        emailRef = FirestoreRef.collection("emails_sent").doc(email.id);
        await emailRef.set({
          ...email,
          sending: true,
          sent: false,
          error: false,
          created_at: momentNow().format(CONSTANTS.MOMENT_ISO),
        });
        console.log(
          `${logPrefix} > FIRESTORE_CREATE > SUCCESS > Documento criado`
        );
      }

      // Marcar como enviando
      if (emailRef) {
        console.log(
          `${logPrefix} > FIRESTORE_UPDATE > Marcando como enviando (tentativa ${attempt})`
        );
        await emailRef.update({
          sending: true,
          sent: false,
          error: false,
          attempt: attempt,
        });
      }

      // Usar a função de envio existente
      console.log(`${logPrefix} > SEND_MAIL > Chamando função sendMail`);
      const result = await sendMail(email, emailRef);

      if (result && result.status === "success") {
        console.log(
          `${logPrefix} > ATTEMPT_${attempt} > SUCCESS > Email enviado com sucesso`
        );
        console.log(
          `${logPrefix} > RESULT > TrackId: ${result.trackId || "N/A"}, Provider: ${result.provider || "unknown"}`
        );
        return {
          success: true,
          attempt: attempt,
          trackId: result.trackId,
          provider: result.provider || "unknown",
        };
      } else {
        lastError = result ? result.error : "Unknown error";
        console.warn(
          `${logPrefix} > ATTEMPT_${attempt} > FAILED > ${lastError}`
        );

        // Se não é a última tentativa, aguardar antes de tentar novamente
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s
          console.log(
            `${logPrefix} > BACKOFF > Aguardando ${delay}ms antes da próxima tentativa`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    } catch (error) {
      lastError = error.message;
      console.error(
        `${logPrefix} > ATTEMPT_${attempt} > ERROR > ${error.message}`
      );

      // Se não é a última tentativa, aguardar antes de tentar novamente
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000;
        console.log(`${logPrefix} > BACKOFF > Aguardando ${delay}ms após erro`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  // Todas as tentativas falharam
  console.error(
    `${logPrefix} > ALL_FAILED > Todas as ${maxRetries} tentativas falharam: ${lastError}`
  );

  return {
    success: false,
    error: lastError || "All retry attempts failed",
    attempts: maxRetries,
  };
};

/**
 * Salva um log de falha definitiva no Firestore
 * @param {Object} email - Dados do email
 * @param {string} error - Mensagem de erro final
 * @param {number} attempts - Número total de tentativas realizadas
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveFailureLog = async (email, error, attempts) => {
  const emailId = email.id || "unknown";
  const logPrefix = `EMAILCRON > FAILURE_LOG > ${emailId}`;

  try {
    console.log(`${logPrefix} > START > Salvando log de falha definitiva`);

    const timestamp = Date.now();
    const logData = {
      // Erro final que causou a falha
      final_error: error,

      // Número total de tentativas realizadas
      total_attempts: attempts,

      // Timestamp da falha definitiva
      failed_at: new Date().toISOString(),
      failed_timestamp: timestamp,

      // Dados básicos do email
      email_to: email.to,
      email_subject: email.subject || "No subject",
      email_from: email.from || "Unknown sender",

      // Dados adicionais para contexto
      scheduled_date: email.scheduled_date,
      source: email.source || "unknown",
      last_error: email.last_error || error,

      // Metadados do log
      log_type: "email_failure",
      created_at: new Date().toISOString(),
    };

    console.log(
      `${logPrefix} > LOG_DATA > To: ${logData.email_to}, Attempts: ${attempts}, Error: ${error}`
    );

    // Salvar log no Firestore: emails/{emailId}/logs/{timestamp}
    const logRef = FirestoreRef.collection("emails")
      .doc(emailId)
      .collection("logs")
      .doc(timestamp.toString());

    await logRef.set(logData);

    console.log(`${logPrefix} > SUCCESS > Log de falha salvo no Firestore`);
    return true;
  } catch (logError) {
    console.error(`${logPrefix} > ERROR > ${logError.message}`);
    console.error(`${logPrefix} > ERROR > Stack: ${logError.stack}`);
    return false;
  }
};

/**
 * Limpa todos os emails da fila (usar com cuidado)
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const clearAllEmails = async () => {
  try {
    const scheduledListKey = "email:scheduled_messages";
    const success = await removeAllMessages(scheduledListKey);

    if (success) {
      console.log("EMAILCRON > CLEAR ALL > All emails cleared from Redis");
    } else {
      console.error("EMAILCRON > CLEAR ALL > Failed to clear emails");
    }

    return success;
  } catch (error) {
    console.error("EMAILCRON > CLEAR ALL > ERROR:", error);
    return false;
  }
};

/**
 * Remove um email específico da fila
 * @param {string} emailId - ID do email
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const deleteEmail = async (emailId) => {
  try {
    const emailKey = `email:message:${emailId}`;
    const scheduledListKey = "email:scheduled_messages";

    const success = await removeMessage(emailKey, scheduledListKey);

    if (success) {
      console.log(
        `EMAILCRON > DELETE EMAIL > Email ${emailId} removed successfully`
      );
    } else {
      console.error(
        `EMAILCRON > DELETE EMAIL > Failed to remove email ${emailId}`
      );
    }

    return success;
  } catch (error) {
    console.error(`EMAILCRON > DELETE EMAIL > ERROR:`, error);
    return false;
  }
};

module.exports = {
  emailSendMessages,
  processScheduledEmails,
  getScheduledEmailsFromRedis,
  sendEmailWithRetry,
  saveFailureLog,
  clearAllEmails,
  deleteEmail,
};
