const {
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const { sendMail } = require("../mailing");
const moment = require("moment");

/**
 * Função principal para processar e enviar emails do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} - Lista de emails processados
 */
const emailSendMessages = async (options = {}) => {
  try {
    // Processar os emails agendados
    const processedEmails = await processScheduledEmails(options);

    console.log(
      `EMAILCRON > EMAIL SEND MESSAGES > Processados ${processedEmails.length} emails`
    );

    return processedEmails;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Busca e imprime os emails agendados do Redis
 * @returns {Promise<Array>} Lista de emails
 */
const getScheduledEmailsFromRedis = async () => {
  try {
    const scheduledListKey = "email:scheduled_messages";
    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "VIEW",
      {
        limit: 100,
        remove: false,
      }
    );

    console.log(
      `EMAILCRON > GET SCHEDULED EMAILS > Found ${emails.length} emails`
    );

    emails.forEach((email, index) => {
      const scheduledDate = new Date(email._scheduled_timestamp).toISOString();
      console.log(
        `EMAILCRON > EMAIL ${index + 1}: To: ${email.to}, Scheduled: ${scheduledDate}, Subject: ${email.subject || "No subject"}`
      );
    });

    return emails;
  } catch (error) {
    console.error("EMAILCRON > GET SCHEDULED EMAILS > ERROR:", error.message);
    return [];
  }
};

/**
 * Processa emails agendados do Redis e os envia
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de emails a processar por vez (default: 20)
 * @param {boolean} options.dryRun - Se true, não envia os emails, apenas simula (default: false)
 * @param {number} options.maxRetries - Número máximo de tentativas por email (default: 3)
 * @returns {Promise<Array>} Lista de emails processados
 */
const processScheduledEmails = async (options = {}) => {
  // Definir opções padrão
  const {
    batchSize = 20, // Menor que mensagens devido ao overhead de email
    dryRun = false,
    maxRetries = 3,
  } = options;

  console.log(
    `EMAILCRON > PROCESS EMAILS > Starting with batchSize: ${batchSize}, dryRun: ${dryRun}`
  );

  try {
    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "email:scheduled_messages";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    // Obter os emails agendados no Redis
    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    if (emails.length === 0) {
      console.log("EMAILCRON > PROCESS EMAILS > NO EMAILS");
      return [];
    }

    const totalEmails = emails.length;
    console.log(
      `EMAILCRON > PROCESS EMAILS > TOTAL EMAILS ${totalEmails} TO PROCESS`
    );

    const processedEmails = [];
    let emailIndex = 0;

    // Processar emails sequencialmente para evitar sobrecarga
    for (const email of emails) {
      try {
        const emailId = email.id || `unknown_${emailIndex}`;

        console.log(
          `EMAILCRON > PROCESS EMAILS > PROCESSING EMAIL ${emailIndex + 1} OF ${totalEmails} > ID: ${emailId}, To: ${email.to}`
        );

        // Se for modo de simulação, não enviar realmente
        if (dryRun) {
          console.log(
            `EMAILCRON > PROCESS EMAILS > [DRY RUN] Would send email ${emailId} to ${email.to}`
          );

          // Simular atualização no Firestore se houver referência
          if (email.firestore_ref) {
            console.log(
              `EMAILCRON > PROCESS EMAILS > [DRY RUN] Would update Firestore for email ${emailId}`
            );
          }

          // Usar a redis_key do email para remoção correta
          if (email.redis_key) {
            await removeMessage(email.redis_key, scheduledListKey);
          }

          processedEmails.push({ ...email, status: "simulated" });
          emailIndex += 1;
          continue;
        }

        // Tentar enviar o email com retry
        const sendResult = await sendEmailWithRetry(email, maxRetries);

        if (sendResult.success) {
          console.log(
            `EMAILCRON > PROCESS EMAILS > EMAIL SENT SUCCESSFULLY > ID: ${emailId}`
          );

          // Remover do Redis após envio bem-sucedido
          if (email.redis_key) {
            await removeMessage(email.redis_key, scheduledListKey);
            console.log(
              `EMAILCRON > PROCESS EMAILS > EMAIL REMOVED FROM REDIS > ${email.redis_key}`
            );
          }

          processedEmails.push({ ...email, status: "sent", ...sendResult });
        } else {
          console.error(
            `EMAILCRON > PROCESS EMAILS > EMAIL FAILED > ID: ${emailId}, Error: ${sendResult.error}`
          );

          // Incrementar contador de tentativas
          const attempts = (email.attempts || 0) + 1;

          if (attempts >= maxRetries) {
            console.error(
              `EMAILCRON > PROCESS EMAILS > EMAIL MAX RETRIES REACHED > ID: ${emailId}, Moving to failed queue`
            );

            // Mover para fila de falhas
            await moveEmailToFailedQueue(email, sendResult.error);

            // Remover da fila principal
            if (email.redis_key) {
              await removeMessage(email.redis_key, scheduledListKey);
            }
          } else {
            console.log(
              `EMAILCRON > PROCESS EMAILS > EMAIL WILL RETRY > ID: ${emailId}, Attempt: ${attempts}/${maxRetries}`
            );

            // Atualizar contador de tentativas no Redis
            const updatedEmail = {
              ...email,
              attempts,
              last_error: sendResult.error,
            };
            // Reagendar para tentativa posterior (5 minutos)
            const retryTime = Date.now() + 5 * 60 * 1000;

            // Remover da posição atual
            if (email.redis_key) {
              await removeMessage(email.redis_key, scheduledListKey);
            }

            // Salvar novamente com novo timestamp
            const { saveScheduledMessage } = require("../utils/redisClient");
            await saveScheduledMessage(
              email.redis_key,
              updatedEmail,
              scheduledListKey,
              retryTime
            );
          }

          processedEmails.push({
            ...email,
            status: "failed",
            error: sendResult.error,
          });
        }

        emailIndex += 1;

        // Pequeno delay entre emails para evitar rate limiting
        if (emailIndex < totalEmails) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error(
          `EMAILCRON > PROCESS EMAILS > UNEXPECTED ERROR processing email ${emailIndex}:`,
          error.message
        );
        emailIndex += 1;
      }
    }

    console.log(
      `EMAILCRON > PROCESS EMAILS > COMPLETED > Processed: ${processedEmails.length}, Total: ${totalEmails}`
    );

    return processedEmails;
  } catch (error) {
    console.error(
      "EMAILCRON > PROCESS EMAILS > CRITICAL ERROR:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Envia um email com sistema de retry
 * @param {Object} email - Dados do email
 * @param {number} maxRetries - Número máximo de tentativas
 * @returns {Promise<Object>} - Resultado do envio
 */
const sendEmailWithRetry = async (email, maxRetries = 3) => {
  let lastError = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(
        `EMAILCRON > SEND EMAIL > Attempt ${attempt}/${maxRetries} for email ${email.id}`
      );

      // Criar referência do Firestore se necessário
      let emailRef = email.firestore_ref;
      if (!emailRef && email.id) {
        // Criar documento no Firestore para rastreamento se não existir
        emailRef = FirestoreRef.collection("emails_sent").doc(email.id);
        await emailRef.set({
          ...email,
          sending: true,
          sent: false,
          error: false,
          created_at: momentNow().format(CONSTANTS.MOMENT_ISO),
        });
      }

      // Marcar como enviando
      if (emailRef) {
        await emailRef.update({
          sending: true,
          sent: false,
          error: false,
          attempt: attempt,
        });
      }

      // Usar a função de envio existente
      const result = await sendMail(email, emailRef);

      if (result && result.status === "success") {
        console.log(
          `EMAILCRON > SEND EMAIL > SUCCESS on attempt ${attempt} for email ${email.id}`
        );
        return {
          success: true,
          attempt: attempt,
          trackId: result.trackId,
          provider: result.provider || "unknown",
        };
      } else {
        lastError = result ? result.error : "Unknown error";
        console.warn(
          `EMAILCRON > SEND EMAIL > FAILED attempt ${attempt}/${maxRetries} for email ${email.id}: ${lastError}`
        );

        // Se não é a última tentativa, aguardar antes de tentar novamente
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s
          console.log(
            `EMAILCRON > SEND EMAIL > Waiting ${delay}ms before retry for email ${email.id}`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    } catch (error) {
      lastError = error.message;
      console.error(
        `EMAILCRON > SEND EMAIL > ERROR on attempt ${attempt}/${maxRetries} for email ${email.id}:`,
        error.message
      );

      // Se não é a última tentativa, aguardar antes de tentar novamente
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  // Todas as tentativas falharam
  console.error(
    `EMAILCRON > SEND EMAIL > ALL ATTEMPTS FAILED for email ${email.id}: ${lastError}`
  );

  return {
    success: false,
    error: lastError || "All retry attempts failed",
    attempts: maxRetries,
  };
};

/**
 * Move um email para a fila de falhas
 * @param {Object} email - Dados do email
 * @param {string} error - Mensagem de erro
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const moveEmailToFailedQueue = async (email, error) => {
  try {
    const failedKey = `email:failed:${email.id}`;
    const failedListKey = "email:failed_messages";

    const failedEmail = {
      ...email,
      failed_at: new Date().toISOString(),
      final_error: error,
      attempts: email.attempts || 0,
    };

    const { saveScheduledMessage } = require("../utils/redisClient");
    const success = await saveScheduledMessage(
      failedKey,
      failedEmail,
      failedListKey,
      Date.now()
    );

    if (success) {
      console.log(
        `EMAILCRON > FAILED QUEUE > Email ${email.id} moved to failed queue`
      );

      // Também salvar no Firestore para auditoria
      try {
        await FirestoreRef.collection("emails_failed")
          .doc(email.id)
          .set({
            ...failedEmail,
            moved_to_failed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
          });
      } catch (firestoreError) {
        console.error(
          "EMAILCRON > FAILED QUEUE > Error saving to Firestore:",
          firestoreError.message
        );
      }
    }

    return success;
  } catch (error) {
    console.error(
      "EMAILCRON > FAILED QUEUE > Error moving email to failed queue:",
      error.message
    );
    return false;
  }
};

/**
 * Limpa todos os emails da fila (usar com cuidado)
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const clearAllEmails = async () => {
  try {
    const scheduledListKey = "email:scheduled_messages";
    const success = await removeAllMessages(scheduledListKey);

    if (success) {
      console.log("EMAILCRON > CLEAR ALL > All emails cleared from Redis");
    } else {
      console.error("EMAILCRON > CLEAR ALL > Failed to clear emails");
    }

    return success;
  } catch (error) {
    console.error("EMAILCRON > CLEAR ALL > ERROR:", error);
    return false;
  }
};

/**
 * Remove um email específico da fila
 * @param {string} emailId - ID do email
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const deleteEmail = async (emailId) => {
  try {
    const emailKey = `email:message:${emailId}`;
    const scheduledListKey = "email:scheduled_messages";

    const success = await removeMessage(emailKey, scheduledListKey);

    if (success) {
      console.log(
        `EMAILCRON > DELETE EMAIL > Email ${emailId} removed successfully`
      );
    } else {
      console.error(
        `EMAILCRON > DELETE EMAIL > Failed to remove email ${emailId}`
      );
    }

    return success;
  } catch (error) {
    console.error(`EMAILCRON > DELETE EMAIL > ERROR:`, error);
    return false;
  }
};

module.exports = {
  emailSendMessages,
  processScheduledEmails,
  getScheduledEmailsFromRedis,
  sendEmailWithRetry,
  moveEmailToFailedQueue,
  clearAllEmails,
  deleteEmail,
};
