/**
 * Script de teste para validar a correção dos parâmetros de timestamp
 * Execute: node test-timestamp-fix.js
 */

const {
  saveScheduledMessage,
  getScheduledMessages,
} = require("./utils/redisClient");
const { momentNow, CONSTANTS } = require("./init");

// Configuração de teste
const TEST_CONFIG = {
  testEmailId: `test_timestamp_${Date.now()}`,
  testEmail: "<EMAIL>",
};

/**
 * Teste 1: Validar ordem correta dos parâmetros em saveScheduledMessage
 */
async function testSaveScheduledMessageParameters() {
  console.log("\n📧 Teste 1: Validação dos parâmetros saveScheduledMessage");

  try {
    const testEmail = {
      id: TEST_CONFIG.testEmailId,
      to: TEST_CONFIG.testEmail,
      from: "<EMAIL>",
      subject: "Teste de Timestamp",
      html: "<h1>Teste de correção de timestamp</h1>",
      source: "test",
    };

    const emailKey = `email:message:${testEmail.id}`;
    const scheduledListKey = "email:scheduled_messages";

    // Criar timestamp para 1 minuto no futuro
    const scheduledTime = Date.now() + 1 * 60 * 1000;

    console.log(`   Email ID: ${testEmail.id}`);
    console.log(`   Email Key: ${emailKey}`);
    console.log(`   Scheduled List Key: ${scheduledListKey}`);
    console.log(
      `   Scheduled Time: ${scheduledTime} (${new Date(scheduledTime).toISOString()})`
    );
    console.log(`   Scheduled Time Type: ${typeof scheduledTime}`);

    // Testar com a ordem correta dos parâmetros
    console.log("   Chamando saveScheduledMessage com ordem correta...");
    const success = await saveScheduledMessage(
      scheduledListKey, // listKey
      scheduledTime, // score (timestamp)
      emailKey, // messageKey
      testEmail // message
    );

    if (success) {
      console.log("✅ saveScheduledMessage executado com sucesso");
      return { emailKey, scheduledListKey, scheduledTime, testEmail };
    } else {
      throw new Error("Falha ao salvar no Redis");
    }
  } catch (error) {
    console.error("❌ Erro no teste saveScheduledMessage:", error.message);
    return null;
  }
}

/**
 * Teste 2: Verificar se o email foi salvo corretamente no Redis
 */
async function testEmailRetrievalFromRedis(testData) {
  console.log("\n🔍 Teste 2: Verificação de recuperação do Redis");

  if (!testData) {
    console.error("❌ Dados de teste não disponíveis");
    return false;
  }

  try {
    const { scheduledListKey, scheduledTime } = testData;

    // Buscar emails agendados até o timestamp de teste + 1 minuto
    const searchTimestamp = scheduledTime + 1 * 60 * 1000;

    console.log(
      `   Buscando emails até: ${searchTimestamp} (${new Date(searchTimestamp).toISOString()})`
    );

    const emails = await getScheduledMessages(
      scheduledListKey,
      searchTimestamp,
      "TEST",
      {
        limit: 10,
        remove: false, // Não remover para não afetar outros testes
      }
    );

    console.log(`   Emails encontrados: ${emails.length}`);

    if (emails.length > 0) {
      const foundEmail = emails.find(
        (email) => email.id === testData.testEmail.id
      );

      if (foundEmail) {
        console.log("✅ Email encontrado no Redis");
        console.log(`   ID: ${foundEmail.id}`);
        console.log(`   To: ${foundEmail.to}`);
        console.log(
          `   Scheduled Timestamp: ${foundEmail._scheduled_timestamp}`
        );
        console.log(`   Scheduled ISO: ${foundEmail._scheduled_iso}`);

        // Validar se o timestamp está correto
        if (foundEmail._scheduled_timestamp === scheduledTime) {
          console.log("✅ Timestamp salvo corretamente");
          return true;
        } else {
          console.error(
            `❌ Timestamp incorreto. Esperado: ${scheduledTime}, Encontrado: ${foundEmail._scheduled_timestamp}`
          );
          return false;
        }
      } else {
        console.error("❌ Email de teste não encontrado nos resultados");
        return false;
      }
    } else {
      console.error("❌ Nenhum email encontrado no Redis");
      return false;
    }
  } catch (error) {
    console.error("❌ Erro na recuperação do Redis:", error.message);
    return false;
  }
}

/**
 * Teste 3: Validar tipos de dados
 */
async function testDataTypes() {
  console.log("\n🔢 Teste 3: Validação de tipos de dados");

  try {
    // Testar diferentes tipos de timestamp
    const testCases = [
      {
        name: "Date.now()",
        value: Date.now(),
        expected: "number",
      },
      {
        name: "new Date().getTime()",
        value: new Date().getTime(),
        expected: "number",
      },
      {
        name: "Date string converted",
        value: new Date("2024-01-15T10:00:00.000Z").getTime(),
        expected: "number",
      },
    ];

    for (const testCase of testCases) {
      const actualType = typeof testCase.value;
      const isValid =
        actualType === testCase.expected && !isNaN(testCase.value);

      console.log(`   ${testCase.name}:`);
      console.log(`     Valor: ${testCase.value}`);
      console.log(`     Tipo: ${actualType}`);
      console.log(`     Válido: ${isValid ? "✅" : "❌"}`);

      if (!isValid) {
        console.error(
          `     ❌ Esperado tipo ${testCase.expected}, obtido ${actualType}`
        );
      }
    }

    console.log("✅ Validação de tipos concluída");
    return true;
  } catch (error) {
    console.error("❌ Erro na validação de tipos:", error.message);
    return false;
  }
}

/**
 * Teste 4: Limpeza dos dados de teste
 */
async function cleanupTestData(testData) {
  console.log("\n🧹 Teste 4: Limpeza de dados de teste");

  if (!testData) {
    console.log("   Nenhum dado de teste para limpar");
    return true;
  }

  try {
    const { emailKey, scheduledListKey } = testData;

    // Remover o email de teste
    const { removeMessage } = require("./utils/redisClient");
    const success = await removeMessage(emailKey, scheduledListKey);

    if (success) {
      console.log(`✅ Dados de teste removidos: ${emailKey}`);
      return true;
    } else {
      console.error(`❌ Falha ao remover dados de teste: ${emailKey}`);
      return false;
    }
  } catch (error) {
    console.error("❌ Erro na limpeza:", error.message);
    return false;
  }
}

/**
 * Função principal de teste
 */
async function runTimestampTests() {
  console.log("🚀 Iniciando testes de correção de timestamp\n");
  console.log("Configuração de teste:", TEST_CONFIG);

  const results = {
    parameterOrder: false,
    redisRetrieval: false,
    dataTypes: false,
    cleanup: false,
  };

  try {
    // Executar testes em sequência
    const testData = await testSaveScheduledMessageParameters();
    results.parameterOrder = !!testData;

    if (results.parameterOrder) {
      results.redisRetrieval = await testEmailRetrievalFromRedis(testData);
    }

    results.dataTypes = await testDataTypes();

    if (testData) {
      results.cleanup = await cleanupTestData(testData);
    } else {
      results.cleanup = true; // Nada para limpar
    }

    // Relatório final
    console.log("\n📊 Relatório Final dos Testes:");
    console.log("================================");

    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? "✅ PASSOU" : "❌ FALHOU";
      console.log(`${test.padEnd(25)}: ${status}`);
    });

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;

    console.log(`\nResultado: ${passedTests}/${totalTests} testes passaram`);

    if (passedTests === totalTests) {
      console.log(
        "🎉 Todos os testes passaram! Correção de timestamp funcionando corretamente."
      );
      console.log("✅ Parâmetros de saveScheduledMessage na ordem correta");
      console.log("✅ Timestamps sendo convertidos corretamente");
      console.log("✅ Dados salvos e recuperados com sucesso");
    } else {
      console.log("⚠️ Alguns testes falharam. Verifique as correções.");
    }
  } catch (error) {
    console.error("\n💥 Erro crítico durante os testes:", error.message);
    console.error(error.stack);
  }
}

// Executar testes se o script for chamado diretamente
if (require.main === module) {
  runTimestampTests()
    .then(() => {
      console.log("\n✨ Testes de timestamp concluídos.");
      process.exit(0);
      return;
    })
    .catch((error) => {
      console.error("\n💥 Erro fatal:", error.message);
      process.exit(1);
    });
}

module.exports = {
  runTimestampTests,
  testSaveScheduledMessageParameters,
  testEmailRetrievalFromRedis,
  testDataTypes,
  cleanupTestData,
};
