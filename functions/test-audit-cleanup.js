/**
 * <PERSON>ript de teste para auditoria e limpeza de dados legados
 * Execute: node test-audit-cleanup.js
 */

const {
  auditAndCleanupLegacyData,
  saveFailureLog,
} = require("./emailSendMessages");
const { FirestoreRef } = require("./init");

// Configuração de teste
const TEST_CONFIG = {
  testEmailId: `test_audit_${Date.now()}`,
  testEmail: "<EMAIL>",
  createTestData: true, // Se deve criar dados de teste antes da auditoria
};

/**
 * Cria dados de teste legados para simular o sistema antigo
 */
async function createTestLegacyData() {
  console.log("\n📝 Criando dados de teste legados...");

  try {
    // Criar alguns documentos na collection emails_failed (sistema antigo)
    const testDocuments = [
      {
        id: `legacy_email_1_${Date.now()}`,
        to: "<EMAIL>",
        subject: "Email Legacy 1",
        failed_at: new Date().toISOString(),
        final_error: "SMTP timeout",
        attempts: 3,
      },
      {
        id: `legacy_email_2_${Date.now()}`,
        to: "<EMAIL>",
        subject: "Email Legacy 2",
        failed_at: new Date().toISOString(),
        final_error: "Connection refused",
        attempts: 3,
      },
      {
        id: `legacy_email_3_${Date.now()}`,
        to: "<EMAIL>",
        subject: "Email Legacy 3",
        failed_at: new Date().toISOString(),
        final_error: "Invalid recipient",
        attempts: 3,
      },
    ];

    console.log(
      `   Criando ${testDocuments.length} documentos de teste na collection emails_failed`
    );

    for (const doc of testDocuments) {
      await FirestoreRef.collection("emails_failed").doc(doc.id).set(doc);
      console.log(`   ✅ Documento criado: ${doc.id}`);
    }

    console.log("✅ Dados de teste legados criados com sucesso");
    return testDocuments.map((doc) => doc.id);
  } catch (error) {
    console.error("❌ Erro ao criar dados de teste:", error.message);
    return [];
  }
}

/**
 * Executa a auditoria e limpeza
 */
async function runAuditCleanup() {
  console.log("\n🔍 Executando auditoria e limpeza de dados legados...");

  try {
    const report = await auditAndCleanupLegacyData();

    console.log("\n📊 Relatório da Auditoria:");
    console.log("==========================");

    // Firestore
    console.log("\n🗄️ Firestore:");
    console.log(
      `   Collection existia: ${report.firestore.collection_existed}`
    );
    console.log(
      `   Documentos encontrados: ${report.firestore.documents_found}`
    );
    console.log(
      `   Documentos removidos: ${report.firestore.documents_removed}`
    );
    console.log(
      `   Limpeza bem-sucedida: ${report.firestore.cleanup_successful}`
    );

    if (report.firestore.sample_document_ids.length > 0) {
      console.log(
        `   IDs de exemplo: ${report.firestore.sample_document_ids.join(", ")}`
      );
    }

    // Redis
    console.log("\n🔴 Redis:");
    console.log(
      `   Fila de falhas existia: ${report.redis.failed_queue_existed}`
    );
    console.log(`   Emails na fila: ${report.redis.emails_in_queue}`);
    console.log(
      `   Chaves individuais encontradas: ${report.redis.individual_keys_found.length}`
    );
    console.log(`   Limpeza bem-sucedida: ${report.redis.cleanup_successful}`);

    if (report.redis.sample_email_keys.length > 0) {
      console.log(
        `   Emails de exemplo: ${report.redis.sample_email_keys.slice(0, 6).join(", ")}`
      );
    }

    if (report.redis.individual_keys_found.length > 0) {
      console.log(
        `   Chaves individuais: ${report.redis.individual_keys_found.slice(0, 5).join(", ")}`
      );
    }

    // Resumo
    console.log("\n📈 Resumo:");
    console.log(
      `   Total de dados legados removidos: ${report.summary.total_legacy_data_removed}`
    );
    console.log(
      `   Limpeza concluída em: ${report.summary.cleanup_completed_at}`
    );
    console.log(
      `   Sistema pronto para novos logs: ${report.summary.system_ready_for_new_logs}`
    );

    return report;
  } catch (error) {
    console.error("❌ Erro durante auditoria:", error.message);
    return null;
  }
}

/**
 * Valida que o novo sistema funciona após a limpeza
 */
async function validateNewSystem() {
  console.log("\n✅ Validando novo sistema de logs...");

  try {
    const testEmail = {
      id: TEST_CONFIG.testEmailId,
      to: TEST_CONFIG.testEmail,
      from: "<EMAIL>",
      subject: "Teste Pós-Limpeza",
      source: "test",
    };

    const finalError = "Test error after cleanup";
    const totalAttempts = 3;

    console.log("   Testando saveFailureLog após limpeza...");
    const success = await saveFailureLog(testEmail, finalError, totalAttempts);

    if (success) {
      console.log("   ✅ Novo sistema de logs funcionando corretamente");

      // Verificar se o log foi salvo na estrutura correta
      const logsSnapshot = await FirestoreRef.collection("emails")
        .doc(testEmail.id)
        .collection("logs")
        .where("log_type", "==", "email_failure")
        .get();

      if (logsSnapshot.size > 0) {
        console.log(
          "   ✅ Log salvo na estrutura correta: emails/{id}/logs/{timestamp}"
        );

        // Limpar dados de teste
        await FirestoreRef.collection("emails").doc(testEmail.id).delete();
        console.log("   ✅ Dados de teste removidos");

        return true;
      } else {
        console.error("   ❌ Log não encontrado na estrutura esperada");
        return false;
      }
    } else {
      console.error("   ❌ Falha ao salvar log com novo sistema");
      return false;
    }
  } catch (error) {
    console.error("❌ Erro na validação do novo sistema:", error.message);
    return false;
  }
}

/**
 * Verifica se ainda existem dados legados após a limpeza
 */
async function verifyCleanupComplete() {
  console.log("\n🔍 Verificando se a limpeza foi completa...");

  try {
    // Verificar Firestore
    const emailsFailedSnapshot =
      await FirestoreRef.collection("emails_failed").get();
    const firestoreClean = emailsFailedSnapshot.size === 0;

    console.log(
      `   Firestore emails_failed: ${firestoreClean ? "✅ Limpo" : "❌ Ainda contém dados"} (${emailsFailedSnapshot.size} documentos)`
    );

    // Verificar Redis (se disponível)
    let redisClean = true;
    try {
      const { getRedisClient } = require("./utils/redisClient");
      const redisClient = await getRedisClient();

      if (redisClient) {
        const queueExists = await redisClient.exists("email:failed_messages");
        const individualKeys = await redisClient.keys("email:failed:*");

        redisClean = queueExists === 0 && individualKeys.length === 0;

        console.log(
          `   Redis fila de falhas: ${queueExists === 0 ? "✅ Removida" : "❌ Ainda existe"}`
        );
        console.log(
          `   Redis chaves individuais: ${individualKeys.length === 0 ? "✅ Removidas" : `❌ ${individualKeys.length} ainda existem`}`
        );
      } else {
        console.log("   Redis: ⚠️ Cliente não disponível para verificação");
      }
    } catch (redisError) {
      console.log(`   Redis: ⚠️ Erro na verificação: ${redisError.message}`);
    }

    const cleanupComplete = firestoreClean && redisClean;

    if (cleanupComplete) {
      console.log("   ✅ Limpeza completa verificada com sucesso");
    } else {
      console.log("   ⚠️ Limpeza pode não estar completa");
    }

    return cleanupComplete;
  } catch (error) {
    console.error("❌ Erro na verificação de limpeza:", error.message);
    return false;
  }
}

/**
 * Função principal de teste
 */
async function runAuditTests() {
  console.log("🚀 Iniciando testes de auditoria e limpeza de dados legados\n");
  console.log("Configuração de teste:", TEST_CONFIG);

  const results = {
    testDataCreated: false,
    auditCompleted: false,
    cleanupSuccessful: false,
    newSystemValidated: false,
    verificationPassed: false,
  };

  try {
    // 1. Criar dados de teste se solicitado
    if (TEST_CONFIG.createTestData) {
      const testIds = await createTestLegacyData();
      results.testDataCreated = testIds.length > 0;
    } else {
      results.testDataCreated = true; // Assumir que dados já existem
    }

    // 2. Executar auditoria e limpeza
    const auditReport = await runAuditCleanup();
    results.auditCompleted = !!auditReport;
    results.cleanupSuccessful = auditReport
      ? auditReport.summary.system_ready_for_new_logs
      : false;

    // 3. Validar novo sistema
    results.newSystemValidated = await validateNewSystem();

    // 4. Verificar se limpeza foi completa
    results.verificationPassed = await verifyCleanupComplete();

    // Relatório final
    console.log("\n📊 Relatório Final dos Testes:");
    console.log("================================");

    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? "✅ PASSOU" : "❌ FALHOU";
      console.log(`${test.padEnd(25)}: ${status}`);
    });

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;

    console.log(`\nResultado: ${passedTests}/${totalTests} testes passaram`);

    if (passedTests === totalTests) {
      console.log(
        "🎉 Todos os testes passaram! Sistema de auditoria funcionando corretamente."
      );
      console.log("✅ Dados legados removidos e novo sistema validado.");
    } else {
      console.log("⚠️ Alguns testes falharam. Verifique os logs acima.");
    }
  } catch (error) {
    console.error("\n💥 Erro crítico durante os testes:", error.message);
    console.error(error.stack);
  }
}

// Executar testes se o script for chamado diretamente
if (require.main === module) {
  runAuditTests()
    .then(() => {
      console.log("\n✨ Testes de auditoria concluídos.");
      process.exit(0);
      return true;
    })
    .catch((error) => {
      console.error("\n💥 Erro fatal:", error.message);
      process.exit(1);
    });
}

module.exports = {
  runAuditTests,
  createTestLegacyData,
  runAuditCleanup,
  validateNewSystem,
  verifyCleanupComplete,
};
