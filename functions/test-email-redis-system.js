/**
 * Script de teste para o sistema Redis de emails
 * Execute: node test-email-redis-system.js
 */

const {
  getRedisClient,
  saveScheduledMessage,
  getScheduledMessages,
  removeMessage,
} = require("./utils/redisClient");
const { emailOrganizeMessages } = require("./emailOrganize");
const {
  emailSendMessages,
  processScheduledEmails,
} = require("./emailSendMessages");
const { emailCronRedis } = require("./mailing");
const { momentNow, CONSTANTS } = require("./init");

// Configuração de teste
const TEST_CONFIG = {
  testEmail: "<EMAIL>",
  batchSize: 5,
  dryRun: true, // Não enviar emails reais durante o teste
};

/**
 * Teste 1: Conexão Redis
 */
async function testRedisConnection() {
  console.log("\n🔧 Teste 1: Conexão Redis");

  try {
    const client = await getRedisClient();
    if (!client) {
      throw new Error("Cliente Redis não disponível");
    }

    // Teste básico de escrita/leitura
    await client.set("test:email:connection", "OK");
    const result = await client.get("test:email:connection");

    if (result === "OK") {
      console.log("✅ Conexão Redis funcionando");
      await client.del("test:email:connection");
      return true;
    } else {
      throw new Error("Teste de escrita/leitura falhou");
    }
  } catch (error) {
    console.error("❌ Erro na conexão Redis:", error.message);
    return false;
  }
}

/**
 * Teste 2: Salvamento de email no Redis
 */
async function testEmailSaveToRedis() {
  console.log("\n📧 Teste 2: Salvamento de email no Redis");

  try {
    const testEmail = {
      id: `test_email_${Date.now()}`,
      to: TEST_CONFIG.testEmail,
      from: "<EMAIL>",
      subject: "Teste do Sistema Redis",
      html: "<h1>Email de teste</h1><p>Este é um email de teste do sistema Redis.</p>",
      scheduled_date: momentNow().add(1, "minute").format(CONSTANTS.MOMENT_ISO),
    };

    const emailKey = `email:message:${testEmail.id}`;
    const scheduledListKey = "email:scheduled_messages";
    const scheduledTime = new Date(testEmail.scheduled_date).getTime();

    const success = await saveScheduledMessage(
      emailKey,
      testEmail,
      scheduledListKey,
      scheduledTime
    );

    if (success) {
      console.log("✅ Email salvo no Redis com sucesso");
      console.log(`   ID: ${testEmail.id}`);
      console.log(`   Para: ${testEmail.to}`);
      console.log(`   Agendado: ${testEmail.scheduled_date}`);
      return testEmail;
    } else {
      throw new Error("Falha ao salvar email no Redis");
    }
  } catch (error) {
    console.error("❌ Erro ao salvar email:", error.message);
    return null;
  }
}

/**
 * Teste 3: Recuperação de emails do Redis
 */
async function testEmailRetrievalFromRedis() {
  console.log("\n📥 Teste 3: Recuperação de emails do Redis");

  try {
    const scheduledListKey = "email:scheduled_messages";
    const currentTime = Date.now();

    const emails = await getScheduledMessages(
      scheduledListKey,
      currentTime + 60 * 1000, // +1 minuto para pegar emails de teste
      "VIEW",
      { limit: 10, remove: false }
    );

    console.log(`✅ Recuperados ${emails.length} emails do Redis`);

    emails.forEach((email, index) => {
      console.log(`   ${index + 1}. ID: ${email.id}, Para: ${email.to}`);
    });

    return emails;
  } catch (error) {
    console.error("❌ Erro ao recuperar emails:", error.message);
    return [];
  }
}

/**
 * Teste 4: Processamento de emails (dry run)
 */
async function testEmailProcessing() {
  console.log("\n⚙️ Teste 4: Processamento de emails (dry run)");

  try {
    const processedEmails = await processScheduledEmails({
      batchSize: TEST_CONFIG.batchSize,
      dryRun: true, // Não enviar emails reais
      maxRetries: 1,
    });

    console.log(`✅ Processados ${processedEmails.length} emails (simulação)`);

    processedEmails.forEach((email, index) => {
      console.log(`   ${index + 1}. ID: ${email.id}, Status: ${email.status}`);
    });

    return processedEmails;
  } catch (error) {
    console.error("❌ Erro no processamento:", error.message);
    return [];
  }
}

/**
 * Teste 5: Sistema completo emailOrganize
 */
async function testEmailOrganizeSystem() {
  console.log("\n🔄 Teste 5: Sistema emailOrganize");

  try {
    const testEmails = [
      {
        id: `organize_test_1_${Date.now()}`,
        to: TEST_CONFIG.testEmail,
        from: "<EMAIL>",
        subject: "Teste Organize 1",
        html: "<p>Email de teste 1 do sistema organize</p>",
        scheduled_date: momentNow()
          .add(2, "minutes")
          .format(CONSTANTS.MOMENT_ISO),
      },
      {
        id: `organize_test_2_${Date.now()}`,
        to: TEST_CONFIG.testEmail,
        from: "<EMAIL>",
        subject: "Teste Organize 2",
        html: "<p>Email de teste 2 do sistema organize</p>",
        scheduled_date: momentNow()
          .add(3, "minutes")
          .format(CONSTANTS.MOMENT_ISO),
      },
    ];

    await emailOrganizeMessages(testEmails);

    console.log("✅ Sistema emailOrganize funcionando");
    console.log(`   Processados ${testEmails.length} emails`);

    return testEmails;
  } catch (error) {
    console.error("❌ Erro no sistema organize:", error.message);
    return [];
  }
}

/**
 * Teste 6: Limpeza de dados de teste
 */
async function cleanupTestData() {
  console.log("\n🧹 Teste 6: Limpeza de dados de teste");

  try {
    const client = await getRedisClient();
    if (!client) {
      throw new Error("Cliente Redis não disponível");
    }

    const scheduledListKey = "email:scheduled_messages";

    // Buscar todas as chaves de teste
    const allKeys = await client.zRange(scheduledListKey, 0, -1);
    const testKeys = allKeys.filter(
      (key) => key.includes("test_email_") || key.includes("organize_test_")
    );

    let removedCount = 0;
    for (const key of testKeys) {
      const success = await removeMessage(key, scheduledListKey);
      if (success) {
        removedCount++;
      }
    }

    console.log(
      `✅ Limpeza concluída: ${removedCount} emails de teste removidos`
    );
    return true;
  } catch (error) {
    console.error("❌ Erro na limpeza:", error.message);
    return false;
  }
}

/**
 * Teste 7: Verificação de integridade da fila
 */
async function testQueueIntegrity() {
  console.log("\n🔍 Teste 7: Integridade da fila");

  try {
    const client = await getRedisClient();
    if (!client) {
      throw new Error("Cliente Redis não disponível");
    }

    const scheduledListKey = "email:scheduled_messages";
    const failedListKey = "email:failed_messages";

    // Verificar tamanhos das filas
    const scheduledCount = await client.zCard(scheduledListKey);
    const failedCount = await client.zCard(failedListKey);

    console.log(`✅ Integridade da fila verificada:`);
    console.log(`   Emails agendados: ${scheduledCount}`);
    console.log(`   Emails falhados: ${failedCount}`);

    // Verificar se há chaves órfãs
    const allKeys = await client.zRange(scheduledListKey, 0, -1);
    let orphanedKeys = 0;

    for (const key of allKeys.slice(0, 10)) {
      // Verificar apenas os primeiros 10
      const exists = await client.exists(key);
      if (!exists) {
        orphanedKeys++;
      }
    }

    if (orphanedKeys > 0) {
      console.warn(`⚠️ Encontradas ${orphanedKeys} chaves órfãs`);
    } else {
      console.log("✅ Nenhuma chave órfã encontrada");
    }

    return { scheduledCount, failedCount, orphanedKeys };
  } catch (error) {
    console.error("❌ Erro na verificação de integridade:", error.message);
    return null;
  }
}

/**
 * Função principal de teste
 */
async function runAllTests() {
  console.log("🚀 Iniciando testes do sistema Redis de emails\n");
  console.log("Configuração de teste:", TEST_CONFIG);

  const results = {
    redisConnection: false,
    emailSave: false,
    emailRetrieval: false,
    emailProcessing: false,
    organizeSystem: false,
    cleanup: false,
    queueIntegrity: false,
  };

  try {
    // Executar testes em sequência
    results.redisConnection = await testRedisConnection();

    if (results.redisConnection) {
      const savedEmail = await testEmailSaveToRedis();
      results.emailSave = !!savedEmail;

      const retrievedEmails = await testEmailRetrievalFromRedis();
      results.emailRetrieval = retrievedEmails.length > 0;

      const processedEmails = await testEmailProcessing();
      results.emailProcessing = processedEmails.length >= 0; // Aceita 0 como sucesso

      const organizedEmails = await testEmailOrganizeSystem();
      results.organizeSystem = organizedEmails.length > 0;

      const integrityCheck = await testQueueIntegrity();
      results.queueIntegrity = !!integrityCheck;

      results.cleanup = await cleanupTestData();
    }

    // Relatório final
    console.log("\n📊 Relatório Final dos Testes:");
    console.log("================================");

    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? "✅ PASSOU" : "❌ FALHOU";
      console.log(`${test.padEnd(20)}: ${status}`);
    });

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;

    console.log(`\nResultado: ${passedTests}/${totalTests} testes passaram`);

    if (passedTests === totalTests) {
      console.log(
        "🎉 Todos os testes passaram! Sistema Redis pronto para uso."
      );
    } else {
      console.log(
        "⚠️ Alguns testes falharam. Verifique a configuração antes de ativar o sistema."
      );
    }
  } catch (error) {
    console.error("\n💥 Erro crítico durante os testes:", error.message);
    console.error(error.stack);
  }
}

// Executar testes se o script for chamado diretamente
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log("\n✨ Testes concluídos.");
      process.exit(0);
      return true;
    })
    .catch((error) => {
      console.error("\n💥 Erro fatal:", error.message);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testRedisConnection,
  testEmailSaveToRedis,
  testEmailRetrievalFromRedis,
  testEmailProcessing,
  testEmailOrganizeSystem,
  cleanupTestData,
  testQueueIntegrity,
};
