const { FirestoreRef, ERROR_TYPES, functions } = require("../init");
const { ROLES, CONSTANTS, COLLECTIONS } = require("../init");
const {
  helpers,
  moment,
  momentNow,
  langMessages,
  adminSMTPConfig,
} = require("../init");
const {
  emailApp,
  mailingApp,
  EMAIL_APP_DOMAIN,
  MAILGUN_API_KEY,
  MAILGUN_SCHEDULE_LIMIT_DAYS,
  MAILGUN_SCHEDULE_LIMIT_TIME,
  DEFAULT_FROM_EMAIL,
  qiplusDomains,
  validateMailbox,
} = require("../mailgun");
const {
  sendEmail,
  validateMailbox: validateResendMailbox,
} = require("../resend");
const {
  isLoopableObject,
  extractShortcodes,
  extractShortcodeFields,
  replaceAll,
  validateEmail,
  sTrim,
  insertPixel,
  insertTrackingLinks,
  insertSubscriptionLinks,
} = helpers;
const {
  fetchPost,
  addNewPost,
  addNewLog,
  fetchCollection,
} = require("../post");
const { replaceShortCodes } = require("../shortcodes");
const { fetchSegmentationsContactIds } = require("../automations");
const nodemailer = require("nodemailer");
const axios = require("axios");
const cors = require("cors")({ origin: true });

// console.log('mailing ------------------------------');
// console.log('post', { fetchPost, addNewPost, addNewLog })
// console.log('replaceShortCodes', { replaceShortCodes })
// console.log('mailing ------------------------------');

const {
  APP_TRIGGERS,
  CRONJOB_TYPES,
  SHORTCODE_PATH_SEPARATOR,
  SHORTCODE_LABEL_SEPARATOR,
  MOMENT_ISO,
} = CONSTANTS;

const { nowToISO, paginateArray } = helpers;

const DEFAULT_EMAIL_SUBJECT = langMessages["email.defaultSubject"];

// grabHereToCopyPaste
const validateEmailAddress = (email) =>
  new Promise((res) => {
    validateMailbox(email, ({ error, body }) => {
      if (error) return res(null);
      return res(body);
    });
  });

// grabHereToCopyPaste
const createMailing = (data, docRef) => {
  let {
    to,
    from,
    fromName,
    cc,
    bcc,
    subject,
    html,
    scheduled_date,
    context,
    owner,
    accountId,
    recipients,
    emailVars,
    smtp,
    smtp_integration,
  } = data;

  let error = false;
  let errorMsg = "";

  // create basic email model for insertion
  from = from || "";
  fromName = fromName || "";
  subject = subject || DEFAULT_EMAIL_SUBJECT;
  cc = cc || "";
  bcc = bcc || "";

  const newEmail = {
    date: data.date || momentNow().format(MOMENT_ISO),
    prepared: true,
    smtp: smtp || CONSTANTS.QIPLUS_SMTP,
    integrationId:
      smtp === (CONSTANTS.OTHER_SMTP || CONSTANTS.MAIL_DOMAIN)
        ? smtp_integration || ""
        : "",
    to,
    from,
    fromName,
    cc,
    bcc,
    subject,
    html,
    scheduled_date,
    emailVars: emailVars || null,
    context,
    owner,
    accountId: accountId || (context || {}).accountId || "",
    error,
    errorMsg,
  };

  if (!html) {
    errorMsg = langMessages["mail.errors.invalidBody"];
  }

  // if ( !fromName ) {
  // 	errorMsg = langMessages["mail.errors.invalidFromName"]
  // }

  if (errorMsg) {
    return addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, {
      ...newEmail,
      error: true,
      errorMsg,
    });
  }

  // console.log('createMailing > newEmail',newEmail);

  // handle recipients
  if (!Array.isArray(recipients)) {
    recipients = [];
  }

  // recipients from "to" string
  const rec = (typeof to === "string" && to.split(",")) || [];
  rec.forEach((t) => {
    if (t && validateEmail(sTrim(t))) {
      recipients.push(sTrim(t));
    }
  });

  // recipients as array of contacts or array of emails
  recipients = recipients
    .filter((r) => {
      return typeof r === "string" && validateEmail(sTrim(r));
    })
    .map((r) => {
      return sTrim(r);
    });

  recipients = [...new Set(recipients)];

  console.log("createMailing > recipients > length", recipients.length);

  const emails = [];

  if (recipients.length) {
    if (recipients.length < 10) {
      console.log("createMailing > recipients", recipients);
    }

    let emailRecip = [];
    let counter = 0;

    for (let r = 0; r < recipients.length; r++) {
      emailRecip.push(recipients[r]);
      counter = r + 1;
      if (counter % 1000 === 0 || counter === recipients.length) {
        const rec = emailRecip.join(", ");
        emailRecip = [];
        emails.push({ ...newEmail, to: rec });
      }
    }
  } else {
    emails.push({
      ...newEmail,
      errorMsg: langMessages["mail.errors.invalidRecipients"],
    });
  }

  console.log("createMailing > emails > length", emails.length);

  const promises = emails.map((mail, i) => {
    if (i === 0 && docRef && docRef.set) {
      return docRef.update(mail);
    }
    return addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, mail);
  });

  return Promise.all(promises)
    .then((results) => {
      console.log("createMailing > results > length", results.length);
      return results;
    })
    .catch((err) => {
      console.error(err);
      return err;
    });
};

// grabHereToCopyPaste
const createBatchMailing = (data, docRef) => {
  console.log("createBatchMailing > data", data);
  // %recipient.yourkey%
  const limit = data.limit || 1000;

  let {
    mailId,
    to,
    from,
    fromName,
    cc,
    bcc,
    subject,
    html,
    scheduled_date,
    context,
    owner,
    accountId,
    recipients,
    smtp,
    smtp_integration,
  } = data;

  let error = false;
  let errorMsg = "";
  let integrationId =
    smtp === (CONSTANTS.OTHER_SMTP || CONSTANTS.MAIL_DOMAIN)
      ? smtp_integration || ""
      : "";

  // create basic email model for insertion
  from = from || "";
  fromName = fromName || "";
  subject = subject || DEFAULT_EMAIL_SUBJECT;
  cc = cc || "";
  bcc = bcc || "";

  const emailError = {
    date: data.date || momentNow().format(MOMENT_ISO),
    prepared: true,
    error: true,
    errorMsg,
    smtp: smtp || CONSTANTS.QIPLUS_SMTP,
    integrationId,
    to,
    from,
    fromName,
    cc,
    bcc,
    subject,
    html,
    scheduled_date,
    context,
    owner,
    accountId: accountId || (context || {}).accountId || "",
  };

  return prepareMailing(data, limit)
    .then((emails) => {
      console.log("PrepareMailing > emails", emails);

      if (emails.error) {
        const { error, errorMessage } = emails;
        const errorMsg =
          typeof errorMessage === "string"
            ? errorMessage
            : typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "PREPARE_EMAIL_ERROR";
        if (docRef && docRef.update) {
          return docRef.update({ ...emailError, error: true, errorMsg });
        }
        return addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, {
          ...emailError,
          error: true,
          errorMsg,
        });
      }

      console.log("createBatchMailing > emails > length", emails.length);
      const promises = emails.map((mail, i) => {
        if (emails.length === 1 && docRef && docRef.update) {
          return docRef.update(mail);
        }
        return addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, mail);
      });

      return Promise.all(promises);
    })
    .catch((error) => {
      console.error(error);
      const errorMsg =
        typeof error === "string"
          ? error
          : error.message
            ? error.message
            : "CREATE_BATCH_MAILING_ERROR";
      return addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, {
        ...emailError,
        error: true,
        errorMsg,
      });
    });
};

// grabHereToCopyPaste
const prepareMailing = async (data, limit) => {
  console.log("PrepareMailing > data MAIL_DOMAIN", data);

  limit = limit || data.limit || 10000;

  let {
    mailId,
    from,
    fromName,
    cc,
    bcc,
    subject,
    html,
    scheduled_date,
    context,
    owner,
    accountId,
    confirmed_broadcast,
    contacts,
    qiusers,
    segmentations,
    smtp,
    smtp_integration,
  } = data;

  if (!Array.isArray(qiusers)) qiusers = [];
  if (!Array.isArray(contacts)) contacts = [];

  let isBroadcast = confirmed_broadcast === true;
  let error = false;
  let errorMsg = "";
  let triggerId = `${new Date().getTime()}`;
  let integrationId =
    smtp === (CONSTANTS.OTHER_SMTP || CONSTANTS.MAIL_DOMAIN)
      ? smtp_integration || ""
      : "";

  console.log("PrepareMailing > integrationId MAIL_DOMAIN", {
    integrationId,
    smtp,
    smtp_integration,
    mail_domain: CONSTANTS.MAIL_DOMAIN,
  });

  // create basic email model for insertion
  from = from || "";
  fromName = fromName || "";
  subject = subject || DEFAULT_EMAIL_SUBJECT;
  cc = cc || "";
  bcc = bcc || "";
  context = context || {};

  const batchEmail = {
    date: data.date || momentNow().format(MOMENT_ISO),
    prepared: true,
    smtp: smtp || CONSTANTS.QIPLUS_SMTP,
    integrationId,
    to: "",
    from,
    fromName,
    cc,
    bcc,
    subject,
    html,
    scheduled_date,
    context,
    triggerId,
    owner,
    accountId: accountId || context.accountId || "",
    error,
    errorMsg,
  };

  if (!html) {
    errorMsg = langMessages["mail.errors.invalidBody"];
  }

  // if ( !fromName ) {
  // 	errorMsg = langMessages["mail.errors.invalidFromName"]
  // }

  if (errorMsg) {
    console.log("PrepareMailing > errorMsg", errorMsg);
    console.log("PrepareMailing > batchEmail", batchEmail);
    return { error: errorMsg };
  }

  const fetchContacts = (contactIds) =>
    new Promise((res, rej) => {
      let promises = [];

      if (contactIds && contactIds.length) {
        let pages = paginateArray(contactIds);
        promises = pages.map((page) =>
          fetchCollection(COLLECTIONS.LEADS_COLLECTION_NAME, [
            ["ID", "in", page],
          ])
        );
      } else if (isBroadcast && accountId) {
        let where = [];
        where.push([CONSTANTS.ACCOUNT_FIELD, "==", accountId]);
        where.push([CONSTANTS.VALID_EMAIL_FIELD, "==", true]);
        where.push([CONSTANTS.EMAIL_FIELD, ">", ""]);
        promises.push(
          fetchCollection(
            COLLECTIONS.LEADS_COLLECTION_NAME,
            where,
            null,
            CONSTANTS.EMAIL_FIELD,
            "asc"
          )
        );
      }

      return Promise.all(promises)
        .then((results) => {
          let filteredContacts = results
            .reduce((all, r) => [...all, ...r], [])
            .filter((c) => {
              return (
                isLoopableObject(c) &&
                c.email &&
                validateEmail(sTrim(c.email)) &&
                (isBroadcast || contactIds.indexOf(c.ID) !== -1)
              );
            })
            .map((c) => {
              return { ...c, email: sTrim(c.email) };
            });
          console.log(
            "PrepareMailing > fetchContacts > filteredContacts",
            filteredContacts.length
          );
          return res(filteredContacts);
        })
        .catch((err) => {
          console.log("PrepareMailing > fetchContacts > err", err);
          return res({ error: "ERROR_FETCHING_CONTACTS" });
        });
    });

  const fetchQIUsers = () =>
    new Promise((res, rej) => {
      let qiusersIds = [...new Set(qiusers)];

      console.log("PrepareMailing > qiusersIds", qiusersIds);

      if (qiusersIds.length) {
        const promises = qiusersIds.map(
          (cId) =>
            new Promise((resPost) =>
              fetchPost(COLLECTIONS.QIUSERS_COLLECTION_NAME, cId)
                .then(resPost)
                .catch(
                  (err) =>
                    console.log("PrepareMailing > fetchPost > err", {
                      cId,
                      err,
                    }) || resPost(null)
                )
            )
        );

        return Promise.all(promises).then((results) => {
          return res(
            results
              .filter(
                (c) =>
                  isLoopableObject(c) &&
                  qiusersIds.indexOf(c.ID) !== -1 &&
                  c.email &&
                  validateEmail(sTrim(c.email))
              )
              .map((c) => {
                return { ...c, email: sTrim(c.email) };
              })
          );
        });
      } else {
        return res([]);
      }
    });

  const getRecipientVariables = (recipientUsers, contextPost) => {
    return new Promise((res, rej) => {
      let recipientVariables = {};

      if (recipientUsers.length) {
        const promises = [];

        recipientUsers.forEach((contactData) => {
          let srcCollection = contactData.collection;
          promises.push(
            replaceShortCodes(html || "", contextPost, srcCollection, [
              contactData,
            ])
          );
        });

        console.log("PrepareMailing > recipientUsers", recipientUsers.length);

        const shortcodes = extractShortcodes(html);

        console.log("PrepareMailing > shortcodes", shortcodes);

        return Promise.all(promises).then((results) => {
          recipientUsers.forEach((contactData, i) => {
            if (results[i]) {
              let shortcodeResults = results[i];
              let { replacements } = shortcodeResults;
              let recipVars = {};

              shortcodes.forEach((s) => {
                let shortcodeData = replacements[s];
                let { shortcodeField, value } = shortcodeData;
                let recipientField = shortcodeField
                  .replace(SHORTCODE_PATH_SEPARATOR, "_")
                  .replace(":", "_");
                recipVars[recipientField] = value;
              });

              recipVars.email = contactData.email;
              recipVars.contactId = contactData.ID;
              recipVars.srcCollection = contactData.collection;

              recipientVariables[contactData.email] = recipVars;
            }
          });

          return res({ recipientVariables });
        });
      } else {
        return res({ recipientVariables });
      }
    });
  };

  try {
    let recipientUsers = [];

    if (isBroadcast) {
      recipientUsers = await fetchContacts();
    } else {
      let segmentationsContacts =
        await fetchSegmentationsContactIds(segmentations);
      let contactIds = [...new Set([...segmentationsContacts, ...contacts])];
      let filteredContacts = await fetchContacts(contactIds);
      let filteredQIUsers = await fetchQIUsers();

      recipientUsers = [...filteredContacts, ...filteredQIUsers];
    }

    console.log("PrepareMailing > recipientUsers", recipientUsers.length);

    let contextPost = {};
    if (context.collection && context.id) {
      contextPost = await fetchPost(context.collection, context.id);
    }

    return getRecipientVariables(recipientUsers, contextPost)
      .then(({ recipientVariables }) => {
        let recipients = Object.keys(recipientVariables);
        let body = html;

        let trackParams = [];
        trackParams.push(
          `${
            CONSTANTS.CONTACT_ID_FIELD
          }/${`%recipient.${CONSTANTS.CONTACT_ID_FIELD}%`}`
        );

        if (mailId) trackParams.push(`mailId/${mailId}`);
        if (triggerId) trackParams.push(`triggerId/${triggerId}`);
        if (accountId) trackParams.push(`accountId/${accountId}`);
        if (integrationId) trackParams.push(`integrationId/${integrationId}`);

        if (context.collection && context.id) {
          trackParams.push(`contextId/${context.id}`);
          trackParams.push(`contextCollection/${context.collection}`);
          if (context.collection === COLLECTIONS.CAMPAIGNS_COLLECTION_NAME) {
            trackParams.push(`${CONSTANTS.CAMPAIGN_FIELD}/${context.id}`);
          }
        }

        /*
      | PIXEL QIPLUS
      */
        body = insertPixel(trackParams, body);

        /*
      | TRACK QIPLUS
      */
        body = insertTrackingLinks(trackParams, body);

        /*
      | SUBSCRIPTION QIPLUS
      */
        body = insertSubscriptionLinks(accountId, `%recipient.email%`, body);

        const shortcodes = extractShortcodes(html);
        const shortcodeFields = extractShortcodeFields(html);

        shortcodeFields.forEach((shortcodeField, i) => {
          let shortcode = shortcodes[i];
          let recipientField = shortcodeField
            .replace(SHORTCODE_PATH_SEPARATOR, "_")
            .replace(":", "_");
          let mailgunShortCode = `%recipient.${recipientField}%`;
          body = replaceAll(shortcode, mailgunShortCode, body);
          batchEmail.html = body;
        });

        console.log("PrepareMailing > recipients > length", recipients.length);
        console.log("PrepareMailing > recipientVariables", recipientVariables);

        const emails = [];

        if (recipients.length) {
          if (recipients.length < 10) {
            console.log("PrepareMailing > recipients", recipients);
          }

          let emailRecip = [];
          let emailVars = {};
          let counter = 0;

          for (let r = 0; r < recipients.length; r++) {
            let recip = recipients[r];
            emailRecip.push(recip);
            emailVars[recip] = recipientVariables[recip];
            counter = r + 1;

            if (counter % limit === 0 || counter === recipients.length) {
              const rec = emailRecip.join(", ");
              emails.push({ ...batchEmail, emailVars, to: rec });
              emailRecip = [];
              emailVars = {};
            }
          }
        } else {
          emails.push({
            ...batchEmail,
            error: true,
            errorMsg: langMessages["mail.errors.emptyRecipients"],
          });
        }

        return emails;
      })
      .catch((err) => {
        console.error(err);
        return { error: err };
      });
  } catch (err) {
    console.error("PrepareMailing > err", err);
    return { error: err };
  }
};

// grabHereToCopyPaste
const createEmail = async (request, response) => {
  cors(request, response, async () => {
    const postData = request.body.data;
    console.log("createEmail > postData", postData);

    if (!postData) {
      // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
      console.log("failed-precondition No data sent", request.body);
      return null;
    }

    const collection = COLLECTIONS.MAIL_COLLECTION_NAME;
    let {
      fromName,
      subject,
      html,
      owner,
      scheduled_date,
      context: { accountId },
    } = postData;

    if (!accountId || !subject || !html || !owner) {
      // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(postData));
      console.log("failed-precondition No data sent", postData);
      return null;
    }

    try {
      const accountDoc = await FirestoreRef.collection(
        COLLECTIONS.ACCOUNTS_COLLECTION_NAME
      )
        .doc(accountId)
        .get();
      const account = accountDoc.exists ? accountDoc.data() : {};

      if ((account || {}).modules) {
        const accountRef = accountDoc.ref;
        const counterDoc = await accountRef
          .collection(COLLECTIONS.INDEX_ID)
          .doc(COLLECTIONS.COUNTER_ID)
          .get();
        const counterData = counterDoc.exists ? counterDoc.data() : {};

        const { modules, config, data } = account;
        console.log("modules", { modules, config });
        let { email_option, emails_towards_base, emails_included } = config;
        let { contacts_max } = data;

        if (collection in modules && !modules[collection]) {
          const errorType = ERROR_TYPES.MODULE_NOT_AVAILABLE;
          const errorMessage = "Este módulo não está disponível na conta atual";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: { collection, modules },
            },
          });
        }

        let limit =
          email_option === "rel"
            ? Number(emails_towards_base) * Number(contacts_max)
            : Number(emails_included);
        let count = 0;

        if (collection in counterData) {
          count = counterData[collection];
        } else {
          let posts = await fetchCollection(collection, [
            [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
          ]);
          count = posts.length;
          console.log("posts", { posts: posts.length, limit });
        }

        if (count && limit && count >= limit) {
          let current = count;
          const errorType = ERROR_TYPES.MODULE_LIMIT_REACHED;
          const errorMessage =
            "O limite de [%collection] da conta foi atingido".replace(
              "[%collection]",
              `emails`
            );
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: {},
              limit,
              current,
            },
          });
        }
      } else {
        console.error("createEmail > No account found");
        const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
        const errorMessage =
          "Ocorreu um erro na requisição. Conta não encontrada.";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: {} },
        });
      }

      let newEmail = {
        ...postData,
        accountId,
        to: postData.to || "",
        prepared: false,
        date: nowToISO(),
      };

      return addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, newEmail)
        .then((post) => {
          let responseData = {
            ...post,
            mailId: post.ID,
            sent: false,
            sending: false,
            error: false,
          };
          return response.send({ data: { ...responseData } });
        })
        .catch((error) => {
          const errorType = ERROR_TYPES.ERROR_ADDING_POST;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: error },
          });
        });
    } catch (error) {
      console.error(error);
      const errorType = ERROR_TYPES.GENERIC_ERROR;
      const errorMessage =
        typeof error === "string" ? error : error.message ? error.message : "";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: error },
      });
    }
  });
  return null;
};

// grabHereToCopyPaste
const prepareEmail = async (request, response) => {
  cors(request, response, async () => {
    const postData = request.body.data;
    console.log("prepareEmail > postData", postData);

    if (!postData) {
      // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
      console.log("failed-precondition No data sent", request.body);
      return null;
    }

    let {
      fromName,
      subject,
      html,
      owner,
      limit,
      scheduled_date,
      context,
      context: { collection },
    } = postData;

    let accountId = context.accountId || postData.accountId;

    if (!accountId || !subject || !html || !owner) {
      // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(postData));
      console.log("failed-precondition No data sent", postData);
      return null;
    }

    try {
      const account = await fetchPost(
        COLLECTIONS.ACCOUNTS_COLLECTION_NAME,
        accountId
      );

      if (!account) {
        const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
        const errorMessage =
          "Ocorreu um erro na requisição. Conta não encontrada.";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: {} },
        });
      }

      let newEmail = {
        ...postData,
        accountId,
        to: postData.to || "",
        prepared: false,
        date: nowToISO(),
      };

      return prepareMailing(newEmail, limit)
        .then((emails) => {
          if (emails.error) {
            const { error } = emails;
            const errorType = ERROR_TYPES.PREPARE_EMAIL_ERROR;
            const errorMessage =
              typeof error === "string"
                ? error
                : error.message
                  ? error.message
                  : "";
            return response.send({
              data: { error: errorType, errorMessage, errorType, debug: error },
            });
          }
          return response.send({
            data: {
              emails: emails.map((e) => ({
                ...e,
                sent: false,
                sending: false,
                error: false,
              })),
            },
          });
        })
        .catch((error) => {
          const errorType = ERROR_TYPES.PREPARE_MAILING_ERROR;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: error },
          });
        });
    } catch (error) {
      console.error(error);
      const errorType = ERROR_TYPES.PREPARE_EMAIL_GENERIC_ERROR;
      const errorMessage =
        typeof error === "string" ? error : error.message ? error.message : "";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: error },
      });
    }
  });
  return null;
};

// grabHereToCopyPaste
const addCronEmail = async (data) => {
  let { subject, html, accountId, scheduled_date, context } = data;

  if (!subject || !html || !accountId) {
    console.log("failed-precondition No data sent", JSON.stringify(data));
    return null;
  }

  const newJob = {
    executed: false,
    execution_date: "",
    scheduled_date,
    date: momentNow().format(MOMENT_ISO),
    modified: momentNow().format(MOMENT_ISO),
    type: CRONJOB_TYPES.BATCH_EMAIL,
    accountId,
    data,
    context,
  };

  try {
    let cronPost = await addNewPost(
      COLLECTIONS.CRONJOBS_COLLECTION_NAME,
      newJob
    );
    return cronPost;
  } catch (err) {
    console.log("err", err);
    return err;
  }
};

// Importar as novas funções da arquitetura Redis
const { emailOrganizeMessages } = require("../emailOrganize");
const { emailSendMessages } = require("../emailSendMessages");

// grabHereToCopyPaste
const emailCronRedis = async () => {
  let now = momentNow().add(5, "minutes").format(MOMENT_ISO);
  console.log("EMAILCRON > START");

  const fetchScheduledEmails = async () => {
    let scheduled = [];

    try {
      const snapshot = await FirestoreRef.collection(
        COLLECTIONS.CRONJOBS_COLLECTION_NAME
      )
        .where("type", "==", CRONJOB_TYPES.BATCH_EMAIL)
        .where("executed", "==", false)
        .where("scheduled_date", "<=", now)
        .get();

      // Coletar emails dos cronjobs sem marcar como executados (sistema Redis irá gerenciar)
      snapshot.docs.forEach((doc) => {
        const job = doc.data();
        if (job.data) {
          scheduled.push({
            ...job.data,
            cronjob_id: doc.id, // Adicionar ID do cronjob para referência
            cronjob_ref: doc.ref, // Referência para atualização posterior pelo Redis
          });
        }
      });

      // NOTA: Não marcamos cronjobs como executados aqui - o sistema Redis irá
      // marcar como executados após o processamento bem-sucedido dos emails

      console.log("EMAILCRON > FETCHED SCHEDULED EMAILS", scheduled.length);
      return scheduled;
    } catch (error) {
      console.error("EMAILCRON > FETCH SCHEDULED EMAILS ERROR", error);
      return [];
    }
  };

  const fetchDirectEmails = async () => {
    let emails = [];

    try {
      const snapshot = await FirestoreRef.collection(
        COLLECTIONS.MAIL_COLLECTION_NAME
      )
        .where("error", "==", false)
        .where("sent", "==", false)
        .where("sending", "==", false)
        .where("scheduled_date", "<=", now)
        .get();

      // Coletar emails sem marcar como processados (sistema Redis irá gerenciar)
      snapshot.forEach((doc) => {
        const data = doc.data();

        // Adicionar ID do documento para referência
        emails.push({
          ...data,
          id: doc.id,
          firestore_ref: doc.ref,
        });
      });

      // NOTA: Não marcamos como processados aqui - o sistema Redis irá gerenciar
      // o status dos emails após o envio bem-sucedido

      console.log("EMAILCRON > FETCHED DIRECT EMAILS", emails.length);
      return emails;
    } catch (error) {
      console.error("EMAILCRON > FETCH DIRECT EMAILS ERROR", error);
      return [];
    }
  };

  try {
    // Buscar emails agendados via cronjobs
    const scheduledEmails = await fetchScheduledEmails();
    if (scheduledEmails && scheduledEmails.length > 0) {
      console.log(
        "EMAILCRON > PROCESSING SCHEDULED EMAILS",
        scheduledEmails.length
      );
      await emailOrganizeMessages(scheduledEmails);
    }

    // Buscar e processar emails diretos
    const directEmails = await fetchDirectEmails();
    if (directEmails && directEmails.length > 0) {
      console.log("EMAILCRON > PROCESSING DIRECT EMAILS", directEmails.length);
      await emailOrganizeMessages(directEmails);
    }

    return null;
  } catch (error) {
    console.error("EMAILCRON > ERROR", error);
    return null;
  }
};

// grabHereToCopyPaste
const emailCron = () => {
  let now = momentNow().format(MOMENT_ISO);

  const fetchCronEmails = () => {
    return new Promise((res, rej) => {
      return FirestoreRef.collection(COLLECTIONS.CRONJOBS_COLLECTION_NAME)
        .where("type", "==", CRONJOB_TYPES.BATCH_EMAIL)
        .where("executed", "==", false)
        .where("scheduled_date", "<=", now)
        .get()
        .then((snapshot) => {
          let promises = [];
          snapshot.forEach((doc) => {
            const job = doc.data();
            const { data } = job;
            promises.push(createBatchMailing(data));
            doc.ref.update({
              executed: true,
              execution_date: momentNow().format(MOMENT_ISO),
            });
          });

          return res({ promises });
        })
        .catch((err) => console.error(err) || rej(err));
    });
  };

  const fetchEmails = () => {
    return new Promise((res) => {
      let promises = [];
      let emails = [];

      return FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
        .where("error", "==", false)
        .where("sent", "==", false)
        .where("sending", "==", false)
        .where("scheduled_date", "<=", now)
        .get()
        .then((snapshot) => {
          snapshot.forEach((doc) => {
            const data = doc.data();
            const docRef = doc.ref;

            if ("prepared" in data && data.prepared === false) {
              promises.push(docRef.update({ prepared: true }));
              promises.push(createBatchMailing(data, docRef));
            } else {
              let { scheduled_date } = data;

              let momentNowInstance = momentNow();
              let scheduledMoment =
                scheduled_date && moment(scheduled_date).isValid()
                  ? moment(scheduled_date)
                  : momentNowInstance;
              let scheduledISO = scheduledMoment.format(CONSTANTS.MOMENT_ISO);
              let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);

              emails.push(data);

              console.log("emailCron > scheduledISO", scheduledISO);
              console.log("emailCron > momentNowISO", momentNowISO);
              console.log(
                "emailCron > scheduledISO > TIME",
                new Date(scheduledISO).getTime()
              );
              console.log(
                "emailCron > momentNowISO > TIME",
                new Date(momentNowISO).getTime()
              );

              if (
                new Date(scheduledISO).getTime() <=
                new Date(momentNowISO).getTime()
              ) {
                promises.push(sendMail(data, docRef));
              }
            }

            return null;
          });

          return res({ emails, promises, err: false });
        })
        .catch((err) => console.error(err) || res({ emails, promises, err }));
    });
  };

  return fetchCronEmails()
    .then(({ promises }) => {
      return Promise.all(promises);
    })
    .then((batchEmails) => {
      return fetchEmails();
    })
    .then(({ emails, promises, err }) => {
      console.log("fetchEmails > results", {
        err,
        emails,
        promises: promises && promises.length,
      });
      if (promises && promises.length) {
        return Promise.all(promises);
      }
      return null;
    })
    .then((result) => {
      console.log("emailCron > result", result);
      return null;
    })
    .catch((error) => {
      console.error(error);
      return null;
    });
};

// grabHereToCopyPaste
const adminMail = async (data) => {
  console.log("adminMail > data", { data });

  // nodemailer
  const nodemailer = require("nodemailer");

  const smtp = (data.config || {}).host ? data.config : adminSMTPConfig;
  const { user, pass, name, service, host, port, encryption } = smtp;

  const {
    message: { to, cc, bcc, from, subject, html },
  } = data;

  let formattedFrom =
    Boolean(from) && from.match(/>/g)
      ? from
      : name
        ? `${name} <${user}>`
        : user;

  let email = { to, from: formattedFrom, subject, html };

  if (cc) email.cc = cc;
  if (bcc) email.bcc = bcc;

  console.log("adminMail > email", email);

  let config = {
    // secure: Boolean(encryption.match(/ssl|tls/)), // use SSL
    tls: {
      // do not fail on invalid certs
      rejectUnauthorized: false,
    },
    auth: { user, pass },
  };

  if (service) {
    config = { ...config, service };
  } else {
    config = { ...config, host, port };
  }

  console.log("adminMail > config", config);

  let transporter = nodemailer.createTransport(config);

  return new Promise((resolve) => {
    transporter.sendMail(email, (err, info) => {
      if (err) {
        console.log("adminMail > err", { err });
        return resolve({
          ...data,
          error: "ERROR_SENDING_EMAIL",
          err,
          status: false,
        });
      }
      console.log("adminMail > info", { info });
      return resolve({ ...info, status: true });
    });
  });
};

// grabHereToCopyPaste
const sendMail = async (data, emailRef) => {
  console.log("sendMail > data", data);

  let {
    to,
    cc,
    bcc,
    from,
    fromName,
    subject,
    scheduled_date,
    context,
    html,
    sent,
    sending,
    error,
    errorMsg,
    emailVars,
    owner,
    accountId,
    tags,
    mailId,
    triggerId,
    smtp,
    integrationId,
  } = data;

  accountId = accountId || CONSTANTS.ORPHANS_ACCOUNT;

  cc =
    (sTrim(cc) &&
      cc
        .split(",")
        .filter((c) => validateEmail(sTrim(c)))
        .map((c) => sTrim(c))
        .join(",")) ||
    "";
  bcc =
    (sTrim(bcc) &&
      bcc
        .split(",")
        .filter((c) => validateEmail(sTrim(c)))
        .map((c) => sTrim(c))
        .join(",")) ||
    "";

  let sendPromise;
  triggerId = triggerId || mailId;

  if (!sent && !sending && !error && !errorMsg && to && html) {
    console.log("sendMail > start");

    let unsubscribed = [];

    try {
      const unsubscribedDocs = await FirestoreRef.collection(
        COLLECTIONS.ACCOUNTS_COLLECTION_NAME
      )
        .doc(accountId)
        .collection(COLLECTIONS.UNSUBSCRIBES_SUBCOLLECTION_NAME)
        .get();
      unsubscribed = unsubscribedDocs.docs.map((d) => d.data());
    } catch (err) {
      console.error(err);
    }

    from = from || DEFAULT_FROM_EMAIL;

    let domain = from.split("@")[1];

    // Define qual serviço de email será usado
    const { MAILGUN_SERVICE, RESEND_SERVICE } = CONSTANTS;
    const DEFAULT_EMAIL_SERVICE = functions.config().email.service || "mailgun";
    const isMailgunService = DEFAULT_EMAIL_SERVICE === MAILGUN_SERVICE;
    const isResendService = DEFAULT_EMAIL_SERVICE === RESEND_SERVICE;

    if (smtp === CONSTANTS.OTHER_SMTP && integrationId) {
      let integration;

      try {
        integration = await fetchPost(
          COLLECTIONS.INTEGRATIONS_COLLECTION_NAME,
          integrationId
        );
      } catch (err) {
        console.error(err);
      }

      if (integration && (integration.data || {}).smtp_config) {
        const { from_email, from_name, host, pass, port, service, encryption } =
          integration.data.smtp_config;

        from = from_email || from;

        const formattedFrom =
          fromName || from_name ? `${fromName || from_name} <${from}>` : from;
        const msg = { to, cc, bcc, from: formattedFrom, subject, html };
        if (!cc) delete msg.cc;
        if (!bcc) delete msg.bcc;

        if (!isLoopableObject(emailVars)) {
          emailVars = {};
          to.split(",").forEach((recipient) => {
            emailVars[recipient.replace(/ /g, "")] = {};
          });
        }

        const promises = Object.keys(emailVars)
          .filter(
            (recipient) => !unsubscribed.find((u) => u.email === recipient)
          )
          .map((recipient, i) => {
            return new Promise(async (res, rej) => {
              let body = html;
              Object.keys(emailVars[recipient]).forEach((recipientField, i) => {
                let val = emailVars[recipient][recipientField];
                let mailgunShortCode = `%recipient.${recipientField}%`;
                body = replaceAll(mailgunShortCode, val, body);
              });

              let trackParams = [];
              let contactId = (emailVars[recipient] || {}).contactId;

              if (contactId) trackParams.push(`contactId/${contactId}`);
              if (mailId) trackParams.push(`mailId/${mailId}`);
              if (triggerId) trackParams.push(`triggerId/${triggerId}`);
              if (integrationId)
                trackParams.push(`integrationId/${integrationId}`);

              if (context.collection && context.id) {
                trackParams.push(`contextId/${context.id}`);
                trackParams.push(`contextCollection/${context.collection}`);
                if (
                  context.collection === COLLECTIONS.CAMPAIGNS_COLLECTION_NAME
                ) {
                  trackParams.push(`${CONSTANTS.CAMPAIGN_FIELD}/${context.id}`);
                }
              }

              /*
            | PIXEL QIPLUS
            */
              body = insertPixel(trackParams, body);

              /*
            | TRACK QIPLUS
            */
              body = insertTrackingLinks(trackParams, body);

              /*
            | SUBSCRIPTION QIPLUS
            */
              body = insertSubscriptionLinks(accountId, recipient, body);

              if (service) {
                const formData = {
                  action: CONSTANTS.SEND_SMTP_MAIL_ACTION,
                  host,
                  port,
                  from_name,
                  from_email,
                  pass,
                  encryption,
                  to: recipient,
                  cc,
                  bcc,
                  subject,
                  message: body,
                };

                console.log("axios > formData", formData);

                const params = new URLSearchParams();
                Object.keys(formData).forEach((key, i) => {
                  params.append(key, formData[key]);
                });

                switch (service) {
                  case "gmail":
                    return axios
                      .post(CONSTANTS.WP_AJAX_URL, params)
                      .then((response) => {
                        const { status, data } = response;
                        console.log("axios > response", { status, data });
                        return res({ ...response, recipient });
                      })
                      .catch((err) => {
                        console.error("axios > err", err);
                        return res({ ...err });
                      });
                  default:
                    return null;
                }
              } else {
                let config = {
                  // secure: true, // use SSL
                  tls: {
                    // do not fail on invalid certs
                    rejectUnauthorized: false,
                  },
                  auth: { user: from_email, pass },
                };

                if (service) {
                  config = {
                    ...config,
                    service,
                  };
                } else {
                  config = {
                    ...config,
                    host,
                    port,
                  };
                }

                console.log("sendMail > transporter > config", { config });

                let transporter = nodemailer.createTransport(config);

                return transporter.sendMail(
                  { ...msg, html: body, to: recipient },
                  (error, info) => {
                    if (error) {
                      console.log("sendMail > transporter > error", { error });
                      return res({ ...error });
                    }
                    console.log("sendMail > transporter > info", { info });
                    return res({
                      ...info,
                      status: 200,
                      data: { sent: true },
                      recipient,
                    });
                  }
                );
              }
            });
          });

        return emailRef
          .update({
            sending: true,
            sent: false,
            error: false,
            errorMsg: "",
          })
          .then((r) => {
            return Promise.all(promises);
          })
          .then((responses) => {
            console.log("sendMail > responses", responses);

            let logs = [];

            const results = responses.map((response) => {
              const { status, data, recipient } = response;

              const vars = emailVars[recipient];
              const contactId = vars.contactId || context.contactId || "";

              const logData = {
                mailId,
                triggerId,
                trackId: response.trackId || response.messageId || "",
                contactId,
                emailVars: vars,
              };

              const log = {
                user_id: contactId,
                contactId,
                operator_id: 0,
                id: context.id || "",
                collection: context.collection || "",
                trigger: "",
                date: momentNow().format(MOMENT_ISO),
                owner,
                accountId,
                data: logData,
                context,
              };

              if (status === 200 && (data || {}).sent) {
                logs.push({
                  ...log,
                  trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
                });
                return true;
              } else {
                logs.push({
                  ...log,
                  trigger: APP_TRIGGERS.APP_TRIGGER_FAILED,
                });
                return false;
              }
            });

            const nowStr = momentNow().format(MOMENT_ISO);
            const promises = logs.map((l) => addNewLog(l));

            if (results.length === results.filter((r) => r === false).length) {
              promises.splice(
                0,
                0,
                emailRef.update({
                  statusCode: error.statusCode || error.code || "",
                  errorMsg: error.msg || "",
                  error: true,
                  sending: false,
                  sent: false,
                  date: data.date || momentNow().format(MOMENT_ISO),
                  errorDate: nowStr,
                })
              );
              // return { status: 'error', error: "ERROR_SENDING_EMAILS" }
            } else {
              promises.splice(
                0,
                0,
                emailRef.update({
                  trackId: "",
                  errorMsg: "",
                  error: false,
                  sending: false,
                  sent: true,
                  date: data.date || momentNow().format(MOMENT_ISO),
                  sentDate: nowStr,
                })
              );
              // return { status: 'success', trackId: '' }
            }

            return Promise.all(promises);
          })
          .catch((err) => {
            console.error(err);

            return emailRef.update({
              sending: false,
              sent: false,
              error: true,
              errorMsg: "",
            });
          });
      } else {
        return emailRef.update({
          sending: false,
          sent: false,
          error: true,
          errorMsg: "No integration found or missing smtp settings",
        });
      }
    } else if (smtp === CONSTANTS.MAIL_DOMAIN) {
      // let integration;

      // try {
      //   integration = await fetchPost(
      //     COLLECTIONS.INTEGRATIONS_COLLECTION_NAME,
      //     integrationId
      //   );
      // } catch (err) {
      //   console.error(err);
      // }

      // if (from) {
      //   //const { host } = integration.data.maildomain_config;

      //   console.log("sendMail > MAIL_DOMAIN", {
      //     integration,

      //     to,
      //     cc,
      //     bcc,
      //     from,
      //     subject,
      //     html,
      //   });

      //   const formattedFrom = from;
      //   const msg = { to, cc, bcc, from: formattedFrom, subject, html };
      //   if (!cc) delete msg.cc;
      //   if (!bcc) delete msg.bcc;

      //   if (!isLoopableObject(emailVars)) {
      //     emailVars = {};
      //     to.split(",").forEach((recipient) => {
      //       emailVars[recipient.replace(/ /g, "")] = {};
      //     });
      //   }

      //   const promises = Object.keys(emailVars)
      //     .filter(
      //       (recipient) => !unsubscribed.find((u) => u.email === recipient)
      //     )
      //     .map((recipient, i) => {
      //       return new Promise(async (res, rej) => {
      //         let body = html;
      //         Object.keys(emailVars[recipient]).forEach((recipientField, i) => {
      //           let val = emailVars[recipient][recipientField];
      //           let mailgunShortCode = `%recipient.${recipientField}%`;
      //           body = replaceAll(mailgunShortCode, val, body);
      //         });

      //         let trackParams = [];
      //         let contactId = (emailVars[recipient] || {}).contactId;

      //         if (contactId) trackParams.push(`contactId/${contactId}`);
      //         if (mailId) trackParams.push(`mailId/${mailId}`);
      //         if (triggerId) trackParams.push(`triggerId/${triggerId}`);
      //         if (integrationId)
      //           trackParams.push(`integrationId/${integrationId}`);

      //         if (context.collection && context.id) {
      //           trackParams.push(`contextId/${context.id}`);
      //           trackParams.push(`contextCollection/${context.collection}`);
      //           if (
      //             context.collection === COLLECTIONS.CAMPAIGNS_COLLECTION_NAME
      //           ) {
      //             trackParams.push(`${CONSTANTS.CAMPAIGN_FIELD}/${context.id}`);
      //           }
      //         }

      //         /*
      // 			| PIXEL QIPLUS
      // 			*/
      //         body = insertPixel(trackParams, body);

      //         /*
      // 			| TRACK QIPLUS
      // 			*/
      //         body = insertTrackingLinks(trackParams, body);

      //         /*
      // 			| SUBSCRIPTION QIPLUS
      // 			*/
      //         body = insertSubscriptionLinks(accountId, recipient, body);

      //         const MAILGUN_API_KEY =
      //           "**************************************************";

      //         const username = "api";
      //         const password = Buffer.from(
      //           `${username}:${MAILGUN_API_KEY}`
      //         ).toString("base64");

      //         let config = {
      //           service: "Mailgun",
      //           auth: {
      //             user: from,
      //             pass: password,
      //           },
      //         };

      //         console.log("sendMail > transporter > config MAIL_DOMAIN", {
      //           config,
      //         });

      //         let transporter = nodemailer.createTransport(config);

      //         return transporter.sendMail(
      //           { ...msg, html: body, to: recipient },
      //           (error, info) => {
      //             if (error) {
      //               console.log("sendMail > transporter > error MAIL_DOMAIN", {
      //                 error,
      //               });
      //               return res({ ...error });
      //             }
      //             console.log("sendMail > transporter > info MAIL_DOMAIN", {
      //               info,
      //             });
      //             return res({
      //               ...info,
      //               status: 200,
      //               data: { sent: true },
      //               recipient,
      //             });
      //           }
      //         );
      //       });
      //     });

      //   return emailRef
      //     .update({
      //       sending: true,
      //       sent: false,
      //       error: false,
      //       errorMsg: "",
      //     })
      //     .then((r) => {
      //       return Promise.all(promises);
      //     })
      //     .then((responses) => {
      //       console.log("sendMail > responses MAIL_DOMAIN", responses);

      //       let logs = [];

      //       const results = responses.map((response) => {
      //         const { status, data, recipient } = response;

      //         const vars = emailVars[recipient];
      //         const contactId = vars.contactId || context.contactId || "";

      //         const logData = {
      //           mailId,
      //           triggerId,
      //           trackId: response.trackId || response.messageId || "",
      //           contactId,
      //           emailVars: vars,
      //         };

      //         const log = {
      //           user_id: contactId,
      //           contactId,
      //           operator_id: 0,
      //           id: context.id || "",
      //           collection: context.collection || "",
      //           trigger: "",
      //           date: momentNow().format(MOMENT_ISO),
      //           owner,
      //           accountId,
      //           data: logData,
      //           context,
      //         };

      //         if (status === 200 && (data || {}).sent) {
      //           logs.push({
      //             ...log,
      //             trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
      //           });
      //           return true;
      //         } else {
      //           logs.push({
      //             ...log,
      //             trigger: APP_TRIGGERS.APP_TRIGGER_FAILED,
      //           });
      //           return false;
      //         }
      //       });

      //       const nowStr = momentNow().format(MOMENT_ISO);
      //       const promises = logs.map((l) => addNewLog(l));

      //       if (results.length === results.filter((r) => r === false).length) {
      //         promises.splice(
      //           0,
      //           0,
      //           emailRef.update({
      //             statusCode: error.statusCode || error.code || "",
      //             errorMsg: error.msg || "",
      //             error: true,
      //             sending: false,
      //             sent: false,
      //             date: data.date || momentNow().format(MOMENT_ISO),
      //             errorDate: nowStr,
      //           })
      //         );
      //         // return { status: 'error', error: "ERROR_SENDING_EMAILS" }
      //       } else {
      //         promises.splice(
      //           0,
      //           0,
      //           emailRef.update({
      //             trackId: "",
      //             errorMsg: "",
      //             error: false,
      //             sending: false,
      //             sent: true,
      //             date: data.date || momentNow().format(MOMENT_ISO),
      //             sentDate: nowStr,
      //           })
      //         );
      //         // return { status: 'success', trackId: '' }
      //       }

      //       return Promise.all(promises);
      //     })
      //     .catch((err) => {
      //       console.error(err);

      //       return emailRef.update({
      //         sending: false,
      //         sent: false,
      //         error: true,
      //         errorMsg: "",
      //       });
      //     });
      // } else {
      //   return emailRef.update({
      //     sending: false,
      //     sent: false,
      //     error: true,
      //     errorMsg: "No integration found or missing domain settings",
      //   });
      // }

      const formattedFrom = from;
      const msg = { to, cc, bcc, from: formattedFrom, subject, html };

      const recipients = msg.to
        .split(",")
        .map((r) => r.replace(/ /g, ""))
        .filter((r) => !unsubscribed.find((u) => u.email === r));

      msg.to = recipients.join(", ");

      if (!cc) delete msg.cc;
      if (!bcc) delete msg.bcc;

      const nowStr = momentNow().format(MOMENT_ISO);
      let momentNowInstance = momentNow();
      let scheduledMoment =
        scheduled_date && moment(scheduled_date).isValid()
          ? moment(scheduled_date)
          : momentNowInstance;
      let scheduledISO = scheduledMoment.format(CONSTANTS.MOMENT_ISO);
      let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);

      const mailgunjs = require("mailgun-js");
      const mailingApi = mailgunjs({
        apiKey: MAILGUN_API_KEY,
        domain: domain,
      });
      const mailDomainAPI = mailingApi.messages();

      let messagesApp = mailDomainAPI;

      if (isLoopableObject(emailVars)) {
        msg["recipient-variables"] = JSON.stringify(emailVars);
        7079635;
      }

      if (new Date(scheduledISO).getTime() > new Date(momentNowISO).getTime()) {
        msg["o:deliverytime"] = new Date(scheduled_date).toUTCString();
      }

      // A single message may be marked with up to 3 tags.
      // let oTags = [...new Set([...(tags||[]), owner, context.collection, context.id ])];
      let oTags = [];
      if (owner) oTags.push(owner);
      if (accountId) oTags.push(accountId);
      if (context.collection) oTags.push(context.collection);
      if (context.id) oTags.push(context.id);

      msg["o:tag"] = oTags;

      sendPromise = new Promise((res, rej) => {
        return messagesApp.send(msg, (error, body) => {
          if (error) {
            console.log("messagesApp > result > error: " + domain, error);

            emailRef.update({
              statusCode: error.statusCode,
              errorMsg: error.msg || `Dominio: ${domain} ${error}`,
              error: true,
              sending: false,
              sent: false,
              date: data.date || momentNow().format(MOMENT_ISO),
              errorDate: nowStr,
            });

            return res({ status: "error", error });
          } else {
            console.log("messagesApp > result > body: " + domain, body);

            let trackId = body.id || "";

            emailRef.update({
              trackId,
              errorMsg: "",
              error: false,
              sending: false,
              sent: true,
              date: data.date || momentNow().format(MOMENT_ISO),
              sentDate: nowStr,
            });

            return res({ status: "success", trackId });
          }
        });
      });

      return emailRef
        .update({
          sending: true,
          sent: false,
          error: false,
          errorMsg: "",
        })
        .then((r) => sendPromise)
        .then(({ status, error, trackId }) => {
          let logs = [];

          if (status === "success" && context.collection) {
            if (isLoopableObject(emailVars)) {
              Object.keys(emailVars).forEach((recip, i) => {
                const vars = emailVars[recip];
                const contactId = vars.contactId || "";

                const logData = {
                  mailId,
                  triggerId,
                  trackId,
                  contactId,
                  emailVars: vars,
                };

                logs.push({
                  user_id: contactId,
                  contactId: contactId,
                  operator_id: 0,
                  id: context.id || "",
                  collection: context.collection || "",
                  trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
                  date: momentNow().format(MOMENT_ISO),
                  owner,
                  accountId,
                  data: logData,
                  context,
                });
              });
            } else {
              const logData = {
                to,
                mailId,
                triggerId,
                trackId,
              };

              logs.push({
                user_id: "",
                contactId: "",
                operator_id: 0,
                id: context.id || "",
                collection: context.collection || "",
                trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
                date: momentNow().format(MOMENT_ISO),
                owner,
                accountId,
                data: logData,
                context,
              });
            }
          }

          const promises = logs.map((l) => addNewLog(l));
          return Promise.all(promises);
        });
    } else if (
      isResendService &&
      (smtp === CONSTANTS.QIPLUS_SMTP || qiplusDomains.includes(domain))
    ) {
      // Usar o Resend para enviar o email
      const formattedFrom = fromName ? `${fromName} <${from}>` : from;

      // Filtrar destinatários que cancelaram a inscrição
      const recipients = to
        .split(",")
        .map((r) => r.replace(/ /g, ""))
        .filter((r) => !unsubscribed.find((u) => u.email === r));

      if (recipients.length === 0) {
        return emailRef.update({
          errorMsg: "Todos os destinatários cancelaram a inscrição",
          error: true,
          sending: false,
          sent: false,
          date: data.date || momentNow().format(MOMENT_ISO),
          errorDate: momentNow().format(MOMENT_ISO),
        });
      }

      // Preparar o corpo do email com variáveis, pixel de rastreamento, etc.
      let processedHtml = html;

      if (isLoopableObject(emailVars)) {
        // Processar variáveis para cada destinatário
        // Como o Resend não suporta variáveis de destinatário como o Mailgun,
        // vamos enviar emails separados para cada destinatário
        const emailPromises = [];

        Object.keys(emailVars)
          .filter(
            (recipient) => !unsubscribed.find((u) => u.email === recipient)
          )
          .forEach((recipient) => {
            let body = html;

            // Substituir variáveis
            Object.keys(emailVars[recipient]).forEach((recipientField) => {
              let val = emailVars[recipient][recipientField];
              let mailgunShortCode = `%recipient.${recipientField}%`;
              body = replaceAll(mailgunShortCode, val, body);
            });

            // Adicionar parâmetros de rastreamento
            let trackParams = [];
            let contactId = (emailVars[recipient] || {}).contactId;

            if (contactId) trackParams.push(`contactId/${contactId}`);
            if (mailId) trackParams.push(`mailId/${mailId}`);
            if (triggerId) trackParams.push(`triggerId/${triggerId}`);

            if (context.collection && context.id) {
              trackParams.push(`contextId/${context.id}`);
              trackParams.push(`contextCollection/${context.collection}`);
              if (
                context.collection === COLLECTIONS.CAMPAIGNS_COLLECTION_NAME
              ) {
                trackParams.push(`${CONSTANTS.CAMPAIGN_FIELD}/${context.id}`);
              }
            }

            // Inserir pixel de rastreamento
            body = insertPixel(trackParams, body);

            // Inserir links de rastreamento
            body = insertTrackingLinks(trackParams, body);

            // Inserir links de cancelamento de inscrição
            body = insertSubscriptionLinks(accountId, recipient, body);

            // Enviar email usando o Resend
            const emailOptions = {
              from: formattedFrom,
              to: recipient,
              subject,
              html: body,
            };

            if (cc) emailOptions.cc = cc;
            if (bcc) emailOptions.bcc = bcc;

            // Adicionar à lista de promessas
            emailPromises.push(
              new Promise((resolve) => {
                sendEmail(emailOptions, (result) => {
                  if (result.error) {
                    console.log("Resend sendEmail error:", result.error);
                    resolve({
                      status: "error",
                      error: result.error,
                      recipient,
                    });
                  } else {
                    console.log("Resend sendEmail success:", result.body);
                    resolve({
                      status: "success",
                      trackId: result.body.id || "",
                      recipient,
                    });
                  }
                });
              })
            );
          });

        // Atualizar o status do email para "enviando"
        return emailRef
          .update({
            sending: true,
            sent: false,
            error: false,
            errorMsg: "",
          })
          .then(() => Promise.all(emailPromises))
          .then((results) => {
            console.log("Resend email results:", results);

            // Verificar se todos os emails falharam
            const allFailed = results.every((r) => r.status === "error");

            // Criar logs para cada email
            const logs = [];

            results.forEach((result) => {
              const { status, trackId, recipient, error } = result;
              const vars = emailVars[recipient] || {};
              const contactId = vars.contactId || context.contactId || "";

              const logData = {
                mailId,
                triggerId,
                trackId: trackId || "",
                contactId,
                emailVars: vars,
              };

              const log = {
                user_id: contactId,
                contactId,
                operator_id: 0,
                id: context.id || "",
                collection: context.collection || "",
                trigger:
                  status === "success"
                    ? APP_TRIGGERS.APP_TRIGGER_SENT
                    : APP_TRIGGERS.APP_TRIGGER_FAILED,
                date: momentNow().format(MOMENT_ISO),
                owner,
                accountId,
                data: logData,
                context,
              };

              logs.push(log);
            });

            // Atualizar o status do email
            const nowStr = momentNow().format(MOMENT_ISO);

            if (allFailed) {
              // Todos os emails falharam
              return emailRef
                .update({
                  errorMsg: "Falha ao enviar todos os emails",
                  error: true,
                  sending: false,
                  sent: false,
                  date: data.date || momentNow().format(MOMENT_ISO),
                  errorDate: nowStr,
                })
                .then(() => Promise.all(logs.map((l) => addNewLog(l))));
            } else {
              // Pelo menos um email foi enviado com sucesso
              const { trackId } = results.find(
                (r) => r.status === "success"
              ) || { trackId: "" };
              return emailRef
                .update({
                  trackId,
                  errorMsg: "",
                  error: false,
                  sending: false,
                  sent: true,
                  date: data.date || momentNow().format(MOMENT_ISO),
                  sentDate: nowStr,
                })
                .then(() => Promise.all(logs.map((l) => addNewLog(l))));
            }
          })
          .catch((err) => {
            console.error("Resend email error:", err);
            return emailRef.update({
              sending: false,
              sent: false,
              error: true,
              errorMsg: err.message || "Erro ao enviar email",
            });
          });
      } else {
        // Sem variáveis de destinatário, enviar um único email para todos
        let trackParams = [];

        if (context.collection && context.id) {
          trackParams.push(`contextId/${context.id}`);
          trackParams.push(`contextCollection/${context.collection}`);
          if (context.collection === COLLECTIONS.CAMPAIGNS_COLLECTION_NAME) {
            trackParams.push(`${CONSTANTS.CAMPAIGN_FIELD}/${context.id}`);
          }
        }

        // Inserir pixel de rastreamento
        processedHtml = insertPixel(trackParams, processedHtml);

        // Inserir links de rastreamento
        processedHtml = insertTrackingLinks(trackParams, processedHtml);

        const emailOptions = {
          from: formattedFrom,
          to: recipients.join(", "),
          subject,
          html: processedHtml,
        };

        if (cc) emailOptions.cc = cc;
        if (bcc) emailOptions.bcc = bcc;

        // Atualizar o status do email para "enviando"
        return emailRef
          .update({
            sending: true,
            sent: false,
            error: false,
            errorMsg: "",
          })
          .then(() => {
            return new Promise((resolve) => {
              sendEmail(emailOptions, (result) => {
                if (result.error) {
                  console.log("Resend sendEmail error:", result.error);
                  resolve({
                    status: "error",
                    error: result.error,
                  });
                } else {
                  console.log("Resend sendEmail success:", result.body);
                  resolve({
                    status: "success",
                    trackId: result.body.id || "",
                  });
                }
              });
            });
          })
          .then((result) => {
            const { status, trackId, error } = result;
            const nowStr = momentNow().format(MOMENT_ISO);

            if (status === "error") {
              const { message } = error || { message: "Falha ao enviar email" };
              // Falha ao enviar o email
              return emailRef.update({
                errorMsg: message,
                error: true,
                sending: false,
                sent: false,
                date: data.date || momentNow().format(MOMENT_ISO),
                errorDate: nowStr,
              });
            } else {
              // Email enviado com sucesso
              const logData = {
                to,
                mailId,
                triggerId,
                trackId,
              };

              const log = {
                user_id: "",
                contactId: "",
                operator_id: 0,
                id: context.id || "",
                collection: context.collection || "",
                trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
                date: momentNow().format(MOMENT_ISO),
                owner,
                accountId,
                data: logData,
                context,
              };

              return emailRef
                .update({
                  trackId,
                  errorMsg: "",
                  error: false,
                  sending: false,
                  sent: true,
                  date: data.date || momentNow().format(MOMENT_ISO),
                  sentDate: nowStr,
                })
                .then(() => addNewLog(log));
            }
          })
          .catch((err) => {
            console.error("Resend email error:", err);
            return emailRef.update({
              sending: false,
              sent: false,
              error: true,
              errorMsg: err.message || "Erro ao enviar email",
            });
          });
      }
    } else if (
      isMailgunService &&
      (smtp === CONSTANTS.QIPLUS_SMTP || qiplusDomains.includes(domain))
    ) {
      const formattedFrom = fromName ? `${fromName} <${from}>` : from;
      const msg = { to, cc, bcc, from: formattedFrom, subject, html };

      const recipients = msg.to
        .split(",")
        .map((r) => r.replace(/ /g, ""))
        .filter((r) => !unsubscribed.find((u) => u.email === r));

      msg.to = recipients.join(", ");

      if (!cc) delete msg.cc;
      if (!bcc) delete msg.bcc;

      const nowStr = momentNow().format(MOMENT_ISO);
      let momentNowInstance = momentNow();
      let scheduledMoment =
        scheduled_date && moment(scheduled_date).isValid()
          ? moment(scheduled_date)
          : momentNowInstance;
      let scheduledISO = scheduledMoment.format(CONSTANTS.MOMENT_ISO);
      let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);

      let messagesApp = domain === EMAIL_APP_DOMAIN ? emailApp : mailingApp;

      if (isLoopableObject(emailVars)) {
        msg["recipient-variables"] = JSON.stringify(emailVars);
      }

      if (new Date(scheduledISO).getTime() > new Date(momentNowISO).getTime()) {
        msg["o:deliverytime"] = new Date(scheduled_date).toUTCString();
      }

      // A single message may be marked with up to 3 tags.
      // let oTags = [...new Set([...(tags||[]), owner, context.collection, context.id ])];
      let oTags = [];
      if (owner) oTags.push(owner);
      if (accountId) oTags.push(accountId);
      if (context.collection) oTags.push(context.collection);
      if (context.id) oTags.push(context.id);

      msg["o:tag"] = oTags;

      sendPromise = new Promise((res, rej) => {
        return messagesApp.send(msg, (error, body) => {
          if (error) {
            console.log("messagesApp > result > error: " + domain, error);

            emailRef.update({
              statusCode: error.statusCode,
              errorMsg: error.msg || "",
              error: true,
              sending: false,
              sent: false,
              date: data.date || momentNow().format(MOMENT_ISO),
              errorDate: nowStr,
            });

            return res({ status: "error", error });
          } else {
            console.log("messagesApp > result > body: " + domain, body);

            let trackId = body.id || "";

            emailRef.update({
              trackId,
              errorMsg: "",
              error: false,
              sending: false,
              sent: true,
              date: data.date || momentNow().format(MOMENT_ISO),
              sentDate: nowStr,
            });

            return res({ status: "success", trackId });
          }
        });
      });

      return emailRef
        .update({
          sending: true,
          sent: false,
          error: false,
          errorMsg: "",
        })
        .then((r) => sendPromise)
        .then(({ status, error, trackId }) => {
          let logs = [];

          if (status === "success" && context.collection) {
            if (isLoopableObject(emailVars)) {
              Object.keys(emailVars).forEach((recip, i) => {
                const vars = emailVars[recip];
                const contactId = vars.contactId || "";

                const logData = {
                  mailId,
                  triggerId,
                  trackId,
                  contactId,
                  emailVars: vars,
                };

                logs.push({
                  user_id: contactId,
                  contactId: contactId,
                  operator_id: 0,
                  id: context.id || "",
                  collection: context.collection || "",
                  trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
                  date: momentNow().format(MOMENT_ISO),
                  owner,
                  accountId,
                  data: logData,
                  context,
                });
              });
            } else {
              const logData = {
                to,
                mailId,
                triggerId,
                trackId,
              };

              logs.push({
                user_id: "",
                contactId: "",
                operator_id: 0,
                id: context.id || "",
                collection: context.collection || "",
                trigger: APP_TRIGGERS.APP_TRIGGER_SENT,
                date: momentNow().format(MOMENT_ISO),
                owner,
                accountId,
                data: logData,
                context,
              });
            }
          }

          const promises = logs.map((l) => addNewLog(l));
          return Promise.all(promises);
        });
    }
  }

  return null;
};

module.exports = {
  validateEmailAddress,
  createMailing,
  createEmail,
  prepareEmail,
  prepareMailing,
  createBatchMailing,
  addCronEmail,
  emailCron,
  emailCronRedis,
  sendMail,
  adminMail,
};
