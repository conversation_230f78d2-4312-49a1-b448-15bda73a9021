const { COLLECTIONS, CONSTANTS, FirestoreRef, momentNow } = require("../init");
const { saveScheduledMessage } = require("../utils/redisClient");
const { replaceShortCodes } = require("../shortcodes");

/**
 * Organiza e prepara emails para serem salvos no Redis
 * @param {Array} emails - Lista de emails para processar
 * @returns {Promise<void>}
 */
const emailOrganizeMessages = async (emails) => {
  console.log("EMAILCRON > ORGANIZE > START > Iniciando organização de emails");

  if (!emails || emails.length === 0) {
    console.log(
      "EMAILCRON > ORGANIZE > NO_EMAILS > Nenhum email para processar"
    );
    return;
  }

  console.log(
    `EMAILCRON > ORGANIZE > BATCH_SIZE > Processando ${emails.length} emails`
  );

  // Contar tipos de email
  const cronjobEmails = emails.filter((e) => e.source === "cronjob").length;
  const directEmails = emails.filter((e) => e.source === "direct").length;

  console.log(
    `EMAILCRON > ORGANIZE > EMAIL_TYPES > Cronjobs: ${cronjobEmails}, Diretos: ${directEmails}`
  );

  try {
    // Processar emails em paralelo
    console.log(
      "EMAILCRON > ORGANIZE > PARALLEL_PROCESS > Iniciando processamento paralelo"
    );
    const results = await Promise.all(
      emails.map((email, index) => {
        console.log(
          `EMAILCRON > ORGANIZE > EMAIL_QUEUE > ${index + 1}/${emails.length} - ID: ${email.id || email.cronjob_id || "unknown"}`
        );
        return prepareEmailAndSaveInRedis(email, index + 1, emails.length);
      })
    );

    // Contar sucessos e falhas
    const successCount = results.filter((r) => r && r.length > 0).length;
    const failureCount = emails.length - successCount;

    console.log(
      `EMAILCRON > ORGANIZE > RESULTS > Sucessos: ${successCount}, Falhas: ${failureCount}`
    );

    if (failureCount > 0) {
      console.error(
        `EMAILCRON > ORGANIZE > WARNING > ${failureCount} emails falharam no processamento`
      );
    }

    console.log(
      "EMAILCRON > ORGANIZE > COMPLETED > SUCCESS > Organização concluída"
    );
  } catch (error) {
    console.error(`EMAILCRON > ORGANIZE > ERROR > ${error.message}`);
    console.error(`EMAILCRON > ORGANIZE > ERROR > Stack: ${error.stack}`);
    throw error;
  }
};

/**
 * Prepara um email individual e salva no Redis
 * @param {Object} email - Objeto de email
 * @param {number} position - Posição na fila (para logs)
 * @param {number} total - Total de emails (para logs)
 * @returns {Promise<Array>} - Lista de emails salvos
 */
const prepareEmailAndSaveInRedis = async (email, position = 0, total = 0) => {
  const emailId =
    email.id ||
    email.cronjob_id ||
    `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const logPrefix = `EMAILCRON > PREPARE > [${position}/${total}] > ${emailId}`;

  try {
    console.log(`${logPrefix} > START > Iniciando preparação`);

    if (!email || !email.to) {
      console.error(
        `${logPrefix} > ERROR > Dados inválidos - To: ${email.to || "undefined"}`
      );
      return [];
    }

    console.log(
      `${logPrefix} > VALIDATION > To: ${email.to}, Source: ${email.source || "unknown"}`
    );

    // Preparar o conteúdo do email com shortcodes se necessário
    let preparedEmail = { ...email };

    // Aplicar shortcodes se houver emailVars
    if (email.emailVars && email.html) {
      console.log(
        `${logPrefix} > SHORTCODES > Aplicando shortcodes - Vars: ${Object.keys(email.emailVars).length}`
      );
      try {
        preparedEmail.html = await replaceShortCodes(
          email.html,
          email.emailVars,
          email.context || {}
        );
        console.log(
          `${logPrefix} > SHORTCODES > SUCCESS > Shortcodes aplicados`
        );
      } catch (error) {
        console.error(`${logPrefix} > SHORTCODES > ERROR > ${error.message}`);
        // Continuar com o HTML original se houver erro nos shortcodes
        console.log(
          `${logPrefix} > SHORTCODES > FALLBACK > Usando HTML original`
        );
      }
    } else {
      console.log(`${logPrefix} > SHORTCODES > SKIP > Sem emailVars ou HTML`);
    }

    // Determinar o tempo de envio
    let scheduledTime;
    if (email.scheduled_date) {
      scheduledTime = new Date(email.scheduled_date).getTime();
      console.log(
        `${logPrefix} > SCHEDULE > Agendado para: ${email.scheduled_date} (${scheduledTime})`
      );
    } else {
      // Enviar imediatamente se não houver data agendada
      scheduledTime = Date.now();
      console.log(
        `${logPrefix} > SCHEDULE > Envio imediato: ${new Date(scheduledTime).toISOString()}`
      );
    }

    // Salvar no Redis
    console.log(`${logPrefix} > REDIS_SAVE > Iniciando salvamento no Redis`);
    const saved = await saveEmailInRedis(emailId, preparedEmail, scheduledTime);

    if (saved) {
      console.log(`${logPrefix} > REDIS_SAVE > SUCCESS > Email salvo no Redis`);

      // Se for um cronjob, marcar como executado
      if (email.cronjob_ref) {
        console.log(
          `${logPrefix} > CRONJOB_UPDATE > Marcando cronjob como executado`
        );
        try {
          await email.cronjob_ref.update({
            executed: true,
            execution_date: momentNow().format(CONSTANTS.MOMENT_ISO),
          });
          console.log(
            `${logPrefix} > CRONJOB_UPDATE > SUCCESS > Cronjob ${email.cronjob_id} marcado como executado`
          );
        } catch (error) {
          console.error(
            `${logPrefix} > CRONJOB_UPDATE > ERROR > ${error.message}`
          );
        }
      }

      console.log(
        `${logPrefix} > COMPLETED > SUCCESS > Email preparado e salvo`
      );
      return [{ ...preparedEmail, id: emailId, redis_saved: true }];
    } else {
      console.error(
        `${logPrefix} > REDIS_SAVE > ERROR > Falha ao salvar no Redis`
      );
      return [];
    }
  } catch (error) {
    console.error(`${logPrefix} > ERROR > ${error.message}`);
    console.error(`${logPrefix} > ERROR > Stack: ${error.stack}`);
    return [];
  }
};

/**
 * Salva um email no Redis com timestamp para agendamento
 * @param {string} emailId - ID único do email
 * @param {Object} email - Dados do email
 * @param {number} scheduledTime - Timestamp para envio
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveEmailInRedis = async (emailId, email, scheduledTime) => {
  const logPrefix = `EMAILCRON > REDIS_SAVE > ${emailId}`;

  try {
    console.log(`${logPrefix} > START > Iniciando salvamento no Redis`);

    // Gerar chave única para o email
    const emailKey = `email:message:${emailId}`;
    const scheduledListKey = "email:scheduled_messages";

    console.log(
      `${logPrefix} > KEYS > EmailKey: ${emailKey}, ListKey: ${scheduledListKey}`
    );

    // Adicionar metadados ao email
    const enrichedEmail = {
      ...email,
      id: emailId,
      _scheduled_timestamp: scheduledTime,
      _scheduled_iso: new Date(scheduledTime).toISOString(),
      _created_at: new Date().toISOString(),
      _type: "email",
      redis_key: emailKey,
    };

    console.log(
      `${logPrefix} > METADATA > To: ${email.to}, ScheduledISO: ${new Date(scheduledTime).toISOString()}, Type: ${enrichedEmail._type}`
    );

    // Garantir que scheduledTime é um timestamp (número)
    const scheduledTimestamp =
      typeof scheduledTime === "number"
        ? scheduledTime
        : new Date(scheduledTime).getTime();

    console.log(`${logPrefix} > REDIS_CLIENT > Chamando saveScheduledMessage`);
    console.log(
      `${logPrefix} > REDIS_CLIENT > ScheduledTime: ${scheduledTimestamp} (type: ${typeof scheduledTimestamp})`
    );

    const success = await saveScheduledMessage(
      scheduledListKey,
      scheduledTimestamp,
      emailKey,
      enrichedEmail
    );

    if (success) {
      console.log(`${logPrefix} > SUCCESS > Email salvo no Redis com sucesso`);
    } else {
      console.error(`${logPrefix} > ERROR > Falha ao salvar email no Redis`);
    }

    return success;
  } catch (error) {
    console.error(`${logPrefix} > ERROR > ${error.message}`);
    console.error(`${logPrefix} > ERROR > Stack: ${error.stack}`);
    return false;
  }
};

module.exports = {
  emailOrganizeMessages,
  prepareEmailAndSaveInRedis,
  saveEmailInRedis,
};
