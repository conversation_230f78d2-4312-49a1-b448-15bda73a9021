const { COLLECTIONS, CONSTANTS, FirestoreRef, momentNow } = require("../init");
const { saveScheduledMessage } = require("../utils/redisClient");
const { replaceShortCodes } = require("../shortcodes");

/**
 * Organiza e prepara emails para serem salvos no Redis
 * @param {Array} emails - Lista de emails para processar
 * @returns {Promise<void>}
 */
const emailOrganizeMessages = async (emails) => {
  if (!emails || emails.length === 0) {
    console.log(
      "EMAILCRON > EMAILORGANIZE > ORGANIZE EMAILS > NO EMAILS TO PROCESS"
    );
    return;
  }

  console.log(
    `EMAILCRON > EMAILORGANIZE > ORGANIZE EMAILS > Processing ${emails.length} emails`
  );

  // Processar emails em paralelo
  await Promise.all(emails.map((email) => prepareEmailAndSaveInRedis(email)));
};

/**
 * Prepara um email individual e salva no Redis
 * @param {Object} email - Objeto de email
 * @returns {Promise<Array>} - Lista de emails salvos
 */
const prepareEmailAndSaveInRedis = async (email) => {
  try {
    if (!email || !email.to) {
      console.log(
        "EMAILCRON > EMAILORGANIZE > PREPARE EMAIL > Invalid email data",
        email && email.id ? email.id : "unknown"
      );
      return [];
    }

    console.log(
      `EMAILCRON > EMAILORGANIZE > PREPARE EMAIL > Processing email ${email.id || "unknown"}`
    );

    // Preparar o conteúdo do email com shortcodes se necessário
    let preparedEmail = { ...email };

    // Aplicar shortcodes se houver emailVars
    if (email.emailVars && email.html) {
      try {
        preparedEmail.html = await replaceShortCodes(
          email.html,
          email.emailVars,
          email.context || {}
        );
      } catch (error) {
        console.error(
          "EMAILCRON > EMAILORGANIZE > SHORTCODE ERROR",
          error.message
        );
        // Continuar com o HTML original se houver erro nos shortcodes
      }
    }

    // Determinar o tempo de envio
    let scheduledTime;
    if (email.scheduled_date) {
      scheduledTime = new Date(email.scheduled_date).getTime();
    } else {
      // Enviar imediatamente se não houver data agendada
      scheduledTime = Date.now();
    }

    // Gerar ID único para o email
    const emailId =
      email.id ||
      `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Salvar no Redis
    const saved = await saveEmailInRedis(emailId, preparedEmail, scheduledTime);

    if (saved) {
      console.log(
        `EMAILCRON > EMAILORGANIZE > EMAIL SAVED > ID: ${emailId}, To: ${email.to}`
      );

      // Se for um cronjob, marcar como executado
      if (email.cronjob_ref) {
        try {
          await email.cronjob_ref.update({
            executed: true,
            execution_date: momentNow().format(CONSTANTS.MOMENT_ISO),
          });
          console.log(
            `EMAILCRON > EMAILORGANIZE > CRONJOB MARKED AS EXECUTED > ${email.cronjob_id}`
          );
        } catch (error) {
          console.error(
            "EMAILCRON > EMAILORGANIZE > ERROR UPDATING CRONJOB",
            error.message
          );
        }
      }

      return [{ ...preparedEmail, id: emailId, redis_saved: true }];
    } else {
      console.error(
        `EMAILCRON > EMAILORGANIZE > FAILED TO SAVE EMAIL > ID: ${emailId}`
      );
      return [];
    }
  } catch (error) {
    console.error(
      "EMAILCRON > EMAILORGANIZE > PREPARE EMAIL ERROR",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Salva um email no Redis com timestamp para agendamento
 * @param {string} emailId - ID único do email
 * @param {Object} email - Dados do email
 * @param {number} scheduledTime - Timestamp para envio
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveEmailInRedis = async (emailId, email, scheduledTime) => {
  try {
    // Gerar chave única para o email
    const emailKey = `email:message:${emailId}`;

    // Chave para a lista ordenada de emails agendados
    const scheduledListKey = "email:scheduled_messages";

    // Adicionar metadados ao email
    const enrichedEmail = {
      ...email,
      id: emailId,
      _scheduled_timestamp: scheduledTime,
      _scheduled_iso: new Date(scheduledTime).toISOString(),
      _created_at: new Date().toISOString(),
      _type: "email",
      redis_key: emailKey,
    };

    console.log(
      "EMAILCRON > EMAILORGANIZE > SAVE REDIS > BEFORE SAVE",
      "emailKey:",
      emailKey,
      "scheduledTime:",
      scheduledTime,
      "scheduledISO:",
      new Date(scheduledTime).toISOString(),
      "to:",
      email.to
    );

    // Usar a função existente do redisClient
    const success = await saveScheduledMessage(
      emailKey,
      enrichedEmail,
      scheduledListKey,
      scheduledTime
    );

    if (success) {
      console.log(
        `EMAILCRON > EMAILORGANIZE > EMAIL SAVED TO REDIS > Key: ${emailKey}`
      );
    } else {
      console.error(
        `EMAILCRON > EMAILORGANIZE > FAILED TO SAVE EMAIL TO REDIS > Key: ${emailKey}`
      );
    }

    return success;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAILORGANIZE > SAVE REDIS ERROR",
      error.message
    );
    console.error(error.stack);
    return false;
  }
};

module.exports = {
  emailOrganizeMessages,
  prepareEmailAndSaveInRedis,
  saveEmailInRedis,
};
