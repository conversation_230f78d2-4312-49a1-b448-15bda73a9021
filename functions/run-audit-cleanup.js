#!/usr/bin/env node

/**
 * Script executável para auditoria e limpeza de dados legados
 *
 * USO:
 * node run-audit-cleanup.js [opções]
 *
 * OPÇÕES:
 * --dry-run          Apenas auditoria, sem remoção de dados
 * --create-test      Criar dados de teste antes da auditoria
 * --force            Executar sem confirmação (CUIDADO!)
 * --help             Mostrar esta ajuda
 *
 * EXEMPLOS:
 * node run-audit-cleanup.js --dry-run
 * node run-audit-cleanup.js --create-test
 * node run-audit-cleanup.js --force
 */

const { auditAndCleanupLegacyData } = require("./emailSendMessages");
const {
  createTestLegacyData,
  verifyCleanupComplete,
} = require("./test-audit-cleanup");
const readline = require("readline");

// Configuração baseada em argumentos da linha de comando
const args = process.argv.slice(2);
const config = {
  dryRun: args.includes("--dry-run"),
  createTest: args.includes("--create-test"),
  force: args.includes("--force"),
  help: args.includes("--help"),
};

/**
 * Mostra ajuda do comando
 */
function showHelp() {
  console.log(`
🔧 AUDITORIA E LIMPEZA DE DADOS LEGADOS - Sistema Redis de Emails

DESCRIÇÃO:
  Este script executa uma auditoria completa e limpeza dos dados legados
  do sistema antigo de fila de falhas de emails no Firebase/Redis.

USO:
  node run-audit-cleanup.js [opções]

OPÇÕES:
  --dry-run          Apenas auditoria, sem remoção de dados
  --create-test      Criar dados de teste antes da auditoria
  --force            Executar sem confirmação (CUIDADO!)
  --help             Mostrar esta ajuda

EXEMPLOS:
  node run-audit-cleanup.js --dry-run
    → Apenas verifica quais dados legados existem

  node run-audit-cleanup.js --create-test
    → Cria dados de teste e executa limpeza completa

  node run-audit-cleanup.js --force
    → Executa limpeza sem pedir confirmação

DADOS QUE SERÃO VERIFICADOS/REMOVIDOS:
  ✓ Firestore collection 'emails_failed'
  ✓ Redis fila 'email:failed_messages'
  ✓ Redis chaves 'email:failed:*'

SEGURANÇA:
  ⚠️  Execute primeiro com --dry-run para ver o que será removido
  ⚠️  Faça backup dos dados se necessário para auditoria
  ⚠️  Confirme que não há processos ativos usando o sistema antigo

LOGS:
  Todos os logs seguem o padrão: EMAILCRON > AUDIT_CLEANUP > [AÇÃO] > [DETALHES]
  Use 'grep "EMAILCRON > AUDIT_CLEANUP"' para filtrar logs relevantes
`);
}

/**
 * Solicita confirmação do usuário
 */
function askConfirmation(message) {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === "y" || answer.toLowerCase() === "yes");
    });
  });
}

/**
 * Executa auditoria em modo dry-run
 */
async function runDryRun() {
  console.log(
    "🔍 MODO DRY-RUN: Apenas verificando dados legados (sem remoção)\n"
  );

  try {
    // Simular auditoria sem remoção
    console.log(
      "EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Iniciando verificação de dados legados"
    );

    // Verificar Firestore
    const { FirestoreRef } = require("./init");
    const emailsFailedSnapshot =
      await FirestoreRef.collection("emails_failed").get();

    console.log(
      `EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Firestore: ${emailsFailedSnapshot.size} documentos em emails_failed`
    );

    if (emailsFailedSnapshot.size > 0) {
      const sampleIds = emailsFailedSnapshot.docs
        .slice(0, 5)
        .map((doc) => doc.id);
      console.log(
        `EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Firestore samples: ${sampleIds.join(", ")}`
      );
    }

    // Verificar Redis
    try {
      const { getRedisClient } = require("./utils/redisClient");
      const redisClient = await getRedisClient();

      if (redisClient) {
        const queueExists = await redisClient.exists("email:failed_messages");
        const queueSize = queueExists
          ? await redisClient.zcard("email:failed_messages")
          : 0;
        const individualKeys = await redisClient.keys("email:failed:*");

        console.log(
          `EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Redis: fila existe=${queueExists === 1}, emails=${queueSize}, chaves individuais=${individualKeys.length}`
        );

        if (individualKeys.length > 0) {
          console.log(
            `EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Redis samples: ${individualKeys.slice(0, 5).join(", ")}`
          );
        }
      } else {
        console.log(
          "EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Redis: Cliente não disponível"
        );
      }
    } catch (redisError) {
      console.log(
        `EMAILCRON > AUDIT_CLEANUP > DRY_RUN > Redis: Erro - ${redisError.message}`
      );
    }

    const totalLegacyData = emailsFailedSnapshot.size;

    console.log(`\n📊 RESUMO DRY-RUN:`);
    console.log(`   Total de dados legados encontrados: ${totalLegacyData}`);
    console.log(
      `   Firestore emails_failed: ${emailsFailedSnapshot.size} documentos`
    );

    if (totalLegacyData > 0) {
      console.log(`\n⚠️  Para remover estes dados, execute sem --dry-run`);
      console.log(`   Comando: node run-audit-cleanup.js`);
    } else {
      console.log(`\n✅ Nenhum dado legado encontrado. Sistema já está limpo.`);
    }

    return totalLegacyData;
  } catch (error) {
    console.error(
      `EMAILCRON > AUDIT_CLEANUP > DRY_RUN > ERROR > ${error.message}`
    );
    return -1;
  }
}

/**
 * Função principal
 */
async function main() {
  console.log("🚀 AUDITORIA E LIMPEZA DE DADOS LEGADOS\n");

  // Mostrar ajuda se solicitado
  if (config.help) {
    showHelp();
    return;
  }

  // Mostrar configuração
  console.log("📋 Configuração:");
  console.log(`   Modo dry-run: ${config.dryRun ? "✅ Sim" : "❌ Não"}`);
  console.log(
    `   Criar dados de teste: ${config.createTest ? "✅ Sim" : "❌ Não"}`
  );
  console.log(`   Forçar execução: ${config.force ? "✅ Sim" : "❌ Não"}`);
  console.log("");

  try {
    // Modo dry-run
    if (config.dryRun) {
      await runDryRun();
      return;
    }

    // Criar dados de teste se solicitado
    if (config.createTest) {
      console.log("📝 Criando dados de teste legados...");
      await createTestLegacyData();
      console.log("");
    }

    // Solicitar confirmação se não for modo force
    if (!config.force) {
      console.log("⚠️  ATENÇÃO: Esta operação irá REMOVER PERMANENTEMENTE:");
      console.log(
        '   • Todos os documentos da collection "emails_failed" no Firestore'
      );
      console.log('   • A fila "email:failed_messages" no Redis');
      console.log('   • Todas as chaves "email:failed:*" no Redis');
      console.log("");
      console.log(
        "💡 Dica: Use --dry-run primeiro para ver o que será removido"
      );
      console.log("");

      const confirmed = await askConfirmation(
        "Tem certeza que deseja continuar?"
      );

      if (!confirmed) {
        console.log("❌ Operação cancelada pelo usuário.");
        return;
      }
    }

    // Executar auditoria e limpeza
    console.log("🔧 Executando auditoria e limpeza completa...\n");

    const report = await auditAndCleanupLegacyData();

    // Mostrar relatório resumido
    console.log("\n📊 RELATÓRIO FINAL:");
    console.log("===================");
    console.log(
      `Firestore - Documentos removidos: ${report.firestore.documents_removed}`
    );
    console.log(
      `Redis - Emails na fila removidos: ${report.redis.emails_in_queue}`
    );
    console.log(
      `Redis - Chaves individuais removidas: ${report.redis.individual_keys_found.length}`
    );
    console.log(
      `Total de dados legados removidos: ${report.summary.total_legacy_data_removed}`
    );
    console.log(
      `Sistema pronto para novos logs: ${report.summary.system_ready_for_new_logs ? "✅ Sim" : "❌ Não"}`
    );
    console.log(`Concluído em: ${report.summary.cleanup_completed_at}`);

    // Verificação final
    console.log("\n🔍 Executando verificação final...");
    const verificationPassed = await verifyCleanupComplete();

    if (report.summary.system_ready_for_new_logs && verificationPassed) {
      console.log("\n🎉 AUDITORIA E LIMPEZA CONCLUÍDAS COM SUCESSO!");
      console.log("✅ Todos os dados legados foram removidos");
      console.log("✅ Sistema pronto para usar o novo sistema de logs");
      console.log("✅ Verificação final passou");
    } else {
      console.log("\n⚠️ AUDITORIA CONCLUÍDA COM PROBLEMAS");
      console.log("❌ Algumas operações podem ter falhado");
      console.log("💡 Verifique os logs acima para detalhes");
    }
  } catch (error) {
    console.error("\n💥 ERRO CRÍTICO:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
}

// Executar função principal
if (require.main === module) {
  main()
    .then(() => {
      console.log("\n✨ Processo concluído.");
      process.exit(0);
      return;
    })
    .catch((error) => {
      console.error("\n💥 Erro fatal:", error.message);
      process.exit(1);
    });
}

module.exports = { main, runDryRun, showHelp };
