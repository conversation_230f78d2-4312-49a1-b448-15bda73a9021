/*
Resend Email Service Implementation
*/

const functions = require("firebase-functions");
const { Resend } = require("resend");
const moment = require("moment");
const { CONSTANTS } = require("../init");
const { isset, validateEmail, momentNow } = require("../helpers");

// Configurações do Resend
const resendConfig = functions.config().resend || {};
const RESEND_API_KEY = resendConfig.apikey || process.env.RESEND_API_KEY || "";
const resendClient = RESEND_API_KEY ? new Resend(RESEND_API_KEY) : null;

// Reutilizando as mesmas constantes do Mailgun para manter compatibilidade
const EMAIL_APP_DOMAIN = "qiplus.cloud";
const MAILING_DOMAIN = "mail.qiplus.com.br";
const DEV_MAILING_DOMAIN = "devmail.qiplus.com.br";
const DEFAULT_MAILING_DOMAIN =
  CONSTANTS.APP_ENV === CONSTANTS.DEV_ENV ? DEV_MAILING_DOMAIN : MAILING_DOMAIN;
const DEFAULT_FROM_EMAIL = `mailing@${DEFAULT_MAILING_DOMAIN}`;
const MESSAGES_ENDPOINT = `${CONSTANTS.REMOTE_URL}/mail/messages.php`;
const STORAGE_ENDPOINT = `${CONSTANTS.REMOTE_URL}/mail/store.php`;

const qiplusDomains = [EMAIL_APP_DOMAIN, MAILING_DOMAIN, DEV_MAILING_DOMAIN];

/**
 * Envia um email usando o Resend
 * @param {Object} options - Opções de email
 * @param {Function} callback - Função de callback
 */
const sendEmail = async (options, callback) => {
  try {
    const { from, to, cc, bcc, subject, html, text, attachments } = options;

    // fromMail
    const fromMail = functions.config().email.from;
    const emailData = {
      from: fromMail || from || DEFAULT_FROM_EMAIL,
      to: to.split(",").map((email) => email.trim()),
      subject,
      html,
      text,
    };

    // Adicionar CC se existir
    if (cc) {
      emailData.cc = cc.split(",").map((email) => email.trim());
    }

    // Adicionar BCC se existir
    if (bcc) {
      emailData.bcc = bcc.split(",").map((email) => email.trim());
    }

    // Adicionar anexos se existirem
    if (attachments && attachments.length > 0) {
      emailData.attachments = attachments;
    }

    const { data, error } = await resendClient.emails.send(emailData);

    if (callback) {
      if (error) {
        return callback({ error: error, body: null });
      } else {
        return callback({ error: null, body: data });
      }
    }

    return data;
  } catch (error) {
    console.error("Resend sendEmail error:", error);
    if (callback) {
      return callback({ error, body: null });
    }
    return { error };
  }
};

/**
 * Envia até 100 emails por vez usando o Resend
 * @param {Array} emails - Lista de emails a serem enviados
 * @param {Function} callback - Função de callback
 * @returns {Promise} - Promise que resolve quando todos os emails forem enviados}
 */
const sendBatchEmails = async (emails, callback) => {
  const result = await resendClient.batch.send(emails);
  if (callback) {
    return callback(result);
  }
  return result;
};

/**
 * Valida um endereço de email
 * @param {string} email - Email para validar
 * @param {Function} callback - Função de callback
 */
const validateMailbox = (email, callback) => {
  // Resend não tem API de validação de email, então usamos a função de validação local
  const isValid = validateEmail(email);

  if (callback) {
    return callback({
      error: null,
      body: {
        address: email,
        is_valid: isValid,
        reason: isValid ? null : "Invalid email format",
      },
    });
  }

  return isValid;
};

const addDomain = (domain) => {
  return resendClient.domains.create({ name: domain });
};

const retrieveDomain = (domain) => {
  return resendClient.domains.retrieve(domain);
};

const listDomains = () => {
  return resendClient.domains.list();
};

const deleteDomain = (domainId) => {
  return resendClient.domains.remove(domainId);
};

const updateDomain = ({ id, openTracking, clickTracking, tls }) => {
  return resendClient.domains.update({
    id,
    openTracking,
    clickTracking,
    tls,
  });
};

const verifyDomain = (domainId) => {
  return resendClient.domains.verify(domainId);
};

module.exports = {
  sendEmail,
  sendBatchEmails,
  validateMailbox,
  resendClient,
  addDomain,
  retrieveDomain,
  listDomains,
  deleteDomain,
  updateDomain,
  verifyDomain,
  RESEND_API_KEY,
  EMAIL_APP_DOMAIN,
  MAILING_DOMAIN,
  DEV_MAILING_DOMAIN,
  DEFAULT_MAILING_DOMAIN,
  DEFAULT_FROM_EMAIL,
  qiplusDomains,
  MESSAGES_ENDPOINT,
  STORAGE_ENDPOINT,
};
