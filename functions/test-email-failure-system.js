/**
 * Script de teste para o sistema modificado de falhas de email
 * Execute: node test-email-failure-system.js
 */

const { saveFailureLog } = require("./emailSendMessages");
const { FirestoreRef } = require("./init");

// Configuração de teste
const TEST_CONFIG = {
  testEmailId: `test_failure_${Date.now()}`,
  testEmail: "<EMAIL>",
};

/**
 * Teste 1: Verificar se a função saveFailureLog funciona corretamente
 */
async function testSaveFailureLog() {
  console.log("\n📧 Teste 1: Função saveFailureLog");

  try {
    const testEmail = {
      id: TEST_CONFIG.testEmailId,
      to: TEST_CONFIG.testEmail,
      from: "<EMAIL>",
      subject: "Teste de Falha de Email",
      source: "test",
      last_error: "Previous SMTP timeout",
    };

    const finalError = "Final SMTP connection refused";
    const totalAttempts = 3;

    console.log(`   Testando email ID: ${testEmail.id}`);
    console.log(`   Para: ${testEmail.to}`);
    console.log(`   Erro final: ${finalError}`);
    console.log(`   Tentativas: ${totalAttempts}`);

    const success = await saveFailureLog(testEmail, finalError, totalAttempts);

    if (success) {
      console.log("✅ Log de falha salvo com sucesso");
      return testEmail;
    } else {
      throw new Error("Falha ao salvar log");
    }
  } catch (error) {
    console.error("❌ Erro no teste saveFailureLog:", error.message);
    return null;
  }
}

/**
 * Teste 2: Verificar se o log foi salvo corretamente no Firestore
 */
async function testFirestoreLogRetrieval() {
  console.log("\n🔍 Teste 2: Verificação do log no Firestore");

  try {
    const emailId = TEST_CONFIG.testEmailId;

    // Buscar logs do email no Firestore
    const logsSnapshot = await FirestoreRef.collection("emails")
      .doc(emailId)
      .collection("logs")
      .get();

    console.log(`   Logs encontrados: ${logsSnapshot.size}`);

    if (logsSnapshot.size === 0) {
      throw new Error("Nenhum log encontrado no Firestore");
    }

    // Verificar o conteúdo do log
    const logDoc = logsSnapshot.docs[0];
    const logData = logDoc.data();

    console.log("   Dados do log:");
    console.log(`   - Tipo: ${logData.log_type}`);
    console.log(`   - Para: ${logData.email_to}`);
    console.log(`   - Assunto: ${logData.email_subject}`);
    console.log(`   - Tentativas: ${logData.total_attempts}`);
    console.log(`   - Erro final: ${logData.final_error}`);
    console.log(`   - Data da falha: ${logData.failed_at}`);

    // Validar campos obrigatórios
    const requiredFields = [
      "final_error",
      "total_attempts",
      "failed_at",
      "email_to",
      "email_subject",
      "log_type",
    ];

    for (const field of requiredFields) {
      if (!logData[field]) {
        throw new Error(`Campo obrigatório ausente: ${field}`);
      }
    }

    // Validar valores específicos
    if (logData.log_type !== "email_failure") {
      throw new Error(`Tipo de log incorreto: ${logData.log_type}`);
    }

    if (logData.total_attempts !== 3) {
      throw new Error(
        `Número de tentativas incorreto: ${logData.total_attempts}`
      );
    }

    if (logData.email_to !== TEST_CONFIG.testEmail) {
      throw new Error(`Email destinatário incorreto: ${logData.email_to}`);
    }

    console.log("✅ Log verificado com sucesso no Firestore");
    return logData;
  } catch (error) {
    console.error("❌ Erro na verificação do Firestore:", error.message);
    return null;
  }
}

/**
 * Teste 3: Verificar se não há referências ao sistema de fila de falhas
 */
async function testNoFailedQueueReferences() {
  console.log(
    "\n🚫 Teste 3: Verificação de remoção do sistema de fila de falhas"
  );

  try {
    const { emailSendMessages } = require("./emailSendMessages");
    const moduleExports = require("./emailSendMessages");

    // Verificar se moveEmailToFailedQueue foi removido dos exports
    if (moduleExports.moveEmailToFailedQueue) {
      throw new Error("moveEmailToFailedQueue ainda está nos exports");
    }

    // Verificar se saveFailureLog está nos exports
    if (!moduleExports.saveFailureLog) {
      throw new Error("saveFailureLog não está nos exports");
    }

    console.log("✅ Sistema de fila de falhas removido corretamente");
    console.log("✅ Nova função saveFailureLog disponível");
    return true;
  } catch (error) {
    console.error("❌ Erro na verificação de remoção:", error.message);
    return false;
  }
}

/**
 * Teste 4: Limpeza dos dados de teste
 */
async function cleanupTestData() {
  console.log("\n🧹 Teste 4: Limpeza de dados de teste");

  try {
    const emailId = TEST_CONFIG.testEmailId;

    // Remover documento de teste do Firestore
    await FirestoreRef.collection("emails").doc(emailId).delete();

    console.log(`✅ Dados de teste removidos: ${emailId}`);
    return true;
  } catch (error) {
    console.error("❌ Erro na limpeza:", error.message);
    return false;
  }
}

/**
 * Função principal de teste
 */
async function runFailureSystemTests() {
  console.log("🚀 Iniciando testes do sistema modificado de falhas de email\n");
  console.log("Configuração de teste:", TEST_CONFIG);

  const results = {
    saveFailureLog: false,
    firestoreVerification: false,
    noFailedQueueReferences: false,
    cleanup: false,
  };

  try {
    // Executar testes em sequência
    const savedEmail = await testSaveFailureLog();
    results.saveFailureLog = !!savedEmail;

    if (results.saveFailureLog) {
      const logData = await testFirestoreLogRetrieval();
      results.firestoreVerification = !!logData;
    }

    results.noFailedQueueReferences = await testNoFailedQueueReferences();
    results.cleanup = await cleanupTestData();

    // Relatório final
    console.log("\n📊 Relatório Final dos Testes:");
    console.log("================================");

    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? "✅ PASSOU" : "❌ FALHOU";
      console.log(`${test.padEnd(25)}: ${status}`);
    });

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;

    console.log(`\nResultado: ${passedTests}/${totalTests} testes passaram`);

    if (passedTests === totalTests) {
      console.log(
        "🎉 Todos os testes passaram! Sistema modificado funcionando corretamente."
      );
    } else {
      console.log("⚠️ Alguns testes falharam. Verifique as modificações.");
    }
  } catch (error) {
    console.error("\n💥 Erro crítico durante os testes:", error.message);
    console.error(error.stack);
  }
}

// Executar testes se o script for chamado diretamente
if (require.main === module) {
  runFailureSystemTests()
    .then(() => {
      console.log("\n✨ Testes concluídos.");
      process.exit(0);
      return true;
    })
    .catch((error) => {
      console.error("\n💥 Erro fatal:", error.message);
      process.exit(1);
    });
}

module.exports = {
  runFailureSystemTests,
  testSaveFailureLog,
  testFirestoreLogRetrieval,
  testNoFailedQueueReferences,
  cleanupTestData,
};
